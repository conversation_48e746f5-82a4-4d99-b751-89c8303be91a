<?php
// 启用错误报告以便调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>月度经营数据分析 - 简化测试版</h2>";

// 检查config.php文件
$config_path = '../config.php';
if (file_exists($config_path)) {
    echo "<p style='color: green;'>✓ 找到config.php文件</p>";
    include $config_path;
    
    if (isset($link) && $link) {
        echo "<p style='color: green;'>✓ 数据库连接成功</p>";
        
        // 测试基本查询
        $firstDayOfMonth = date('Y-m-01');
        $lastDayOfMonth = date('Y-m-t');
        $startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
        $endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
        
        echo "<p>查询日期范围: $startDate 到 $endDate</p>";
        
        // 测试服务费查询
        $sql = "SELECT sum(fwf) as fwfhj FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
        echo "<p>SQL查询: $sql</p>";
        
        $result = mysqli_query($link, $sql);
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            $fwfhj = $row["fwfhj"] ? $row["fwfhj"] : 0;
            echo "<p style='color: green;'>✓ 服务费合计查询成功: $fwfhj 万元</p>";
        } else {
            echo "<p style='color: red;'>✗ 服务费查询失败: " . mysqli_error($link) . "</p>";
        }
        
        // 测试产值查询
        $sql = "SELECT IFNULL(SUM(wccz), 0) as hj FROM `tuqoa_xmcztjb` WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'";
        echo "<p>SQL查询: $sql</p>";
        
        $result = mysqli_query($link, $sql);
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            $cz = $row["hj"] ? $row["hj"] : 0;
            echo "<p style='color: green;'>✓ 总产值查询成功: $cz 万元</p>";
        } else {
            echo "<p style='color: red;'>✗ 产值查询失败: " . mysqli_error($link) . "</p>";
        }
        
        // 测试人员查询
        $sql = "SELECT count(*) as hj FROM `tuqoa_rydp` WHERE `status`=1 and `state`='在职' and drxm<>'工程管理部待岗人员'";
        echo "<p>SQL查询: $sql</p>";
        
        $result = mysqli_query($link, $sql);
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            $zgrs = $row["hj"] > 0 ? $row["hj"] : 1;
            echo "<p style='color: green;'>✓ 在职人员查询成功: $zgrs 人</p>";
            
            $rjcz = number_format($cz / $zgrs, 4);
            echo "<p style='color: blue;'>人均产值: $rjcz 万元</p>";
        } else {
            echo "<p style='color: red;'>✗ 人员查询失败: " . mysqli_error($link) . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ 数据库连接失败</p>";
    }
} else {
    echo "<p style='color: red;'>✗ 找不到config.php文件</p>";
    echo "<p>当前目录: " . __DIR__ . "</p>";
    echo "<p>查找路径: $config_path</p>";
}
?>

<hr>
<h3>如果上面的测试都通过，说明基本功能正常</h3>
<p><a href="ydjysjfx.php">点击这里访问完整版页面</a></p>
