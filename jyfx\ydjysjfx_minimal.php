<?php
// 启用错误报告以便调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../config.php';

// 检查数据库连接
if (!isset($link) || !$link) {
    die("数据库连接失败");
}

$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 初始化所有需要的变量
$fwfhj = 0;
$cz = 0;
$zgrs = 1;
$rjcz = 0;

// 计算服务费合计
$sql="SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
$result = mysqli_query($link, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $fwfhj = $row["fwfhj"] ? $row["fwfhj"] : 0;
    }
}

// 计算总产值
$sql="SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb`  WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'";
$result = mysqli_query($link, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $cz = $row["hj"] ? $row["hj"] : 0;
    }
}

// 计算员工人数
$sql="SELECT count(*) hj FROM `tuqoa_rydp` WHERE `status`=1 and `state`='在职' and drxm<>'工程管理部待岗人员'";
$result = mysqli_query($link, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $zgrs = $row["hj"] > 0 ? $row["hj"] : 1;
    }
}
$rjcz = number_format($cz / $zgrs, 4);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>月度经营数据分析 - 最小化版本</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .card { border: 1px solid #ddd; padding: 20px; margin: 10px; border-radius: 5px; }
        .row { display: flex; flex-wrap: wrap; }
        .col { flex: 1; min-width: 200px; }
        h2 { color: #333; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h2>月度经营数据分析</h2>
    
    <div class="card">
        <h3>查询日期范围</h3>
        <form method="post" action="">
            <label for="start-date">开始日期:</label>
            <input type="date" id="start-date" name="start-date" value="<?php echo htmlspecialchars($startDate); ?>">
            <label for="end-date">结束日期:</label>
            <input type="date" id="end-date" name="end-date" value="<?php echo htmlspecialchars($endDate); ?>">
            <button type="submit">查询</button>
        </form>
    </div>
    
    <div class="row">
        <div class="col">
            <div class="card">
                <h3>本月新接合同额</h3>
                <h2 class="success">¥<?=$fwfhj?>万</h2>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <h3>总产值</h3>
                <h2 class="success"><?=$cz?>万</h2>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <h3>人均产值</h3>
                <h2 class="success">¥<?=$rjcz?>万</h2>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <h3>在职人员</h3>
                <h2 class="success"><?=$zgrs?>人</h2>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h3>调试信息</h3>
        <p>查询日期范围: <?=$startDate?> 到 <?=$endDate?></p>
        <p>数据库连接状态: <span class="success">正常</span></p>
        <p>PHP版本: <?=phpversion()?></p>
    </div>
    
    <div class="card">
        <p><strong>如果这个页面正常显示数据，说明基本功能没有问题。</strong></p>
        <p><a href="ydjysjfx.php">点击这里访问完整版页面</a></p>
    </div>
</body>
</html>
