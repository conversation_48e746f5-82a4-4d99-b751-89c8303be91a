<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目成本核算（月度） - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-normal {
            background-color: #4caf50;
            color: #fff;
        }
        .status-warning {
            background-color: #ff9800;
            color: #fff;
        }
        .status-danger {
            background-color: #f44336;
            color: #fff;
        }
        .filter-container {
            margin-bottom: 1rem;
        }
        .chart-container {
            height: 300px;
        }
        .progress {
            height: 1.5rem;
        }
        .progress-bar {
            font-size: 0.75rem;
            font-weight: 600;
        }
        .refresh-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .cost-trend-up {
            color: #f44336;
        }
        .cost-trend-down {
            color: #4caf50;
        }
        .cost-trend-stable {
            color: #2196f3;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        .monthly-cost-chart {
            height: 250px;
        }
        .cost-distribution-chart {
            height: 250px;
        }
        .cost-trend-chart {
            height: 250px;
        }
        .cost-control-chart {
            height: 250px;
        }
        .cost-ratio-chart {
            height: 250px;
        }
        .cost-summary-chart {
            height: 250px;
        }
        .filter-row {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .filter-item {
            margin-right: 1rem;
        }
        .nav-tabs .nav-link {
            color: #495057;
        }
        .nav-tabs .nav-link.active {
            font-weight: 600;
        }
        .tab-content {
            padding: 1rem 0;
        }
        .employee-card {
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1rem;
        }
        .employee-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        .detail-row {
            margin-bottom: 0.5rem;
        }
        .detail-label {
            font-weight: 600;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>项目成本核算（月度）</h2>
                <div class="d-flex align-items-center">
                    <span class="refresh-time me-3">最后更新时间: <span id="last-update-time">2023-06-15 14:30:45</span></span>
                </div>
            </div>
            
            <!-- 项目选择和月份选择放在同一行 -->
            <form method="post" action="">
            <div class="filter-row mb-4">
                <div class="filter-item">
                    <label for="project-select">选择项目：</label>
                    <select id="project-select" class="form-select" style="width: auto; display: inline-block;" name="gcid">
                        <?
                        $gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
                        $sql="SELECT * FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                        $result = mysqli_query($link, $sql);
                            while ($row = mysqli_fetch_assoc($result)) {
                                $selected = ($gcid == $row["id"]) ? 'selected' : '';
                        ?>
                        <option value="<?=$row["id"]?>" <?=$selected?>><?=$row["gcname"]?></option>
                        <?
                            }
                        ?>
                    </select>
                </div>
                <div class="date-range-container">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                    </div>
                <button type="submit" id="query-btn" class="btn btn-primary">提交</button>
                
            </div>
            </form>
            <?
            
            if ($gcid==""){
                $sql="SELECT * FROM `tuqoa_gcproject` order by id DESC LIMIT 1";
            }else{
                $sql="SELECT * FROM `tuqoa_gcproject` WHERE id=$gcid";
            }
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $gcid=$row["id"];
            ?>
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">成本系数</h5>
                            <h2 class="card-text"><?=$row["cbxs"]?></h2>
                        </div>
                    </div>
                </div>
                <?
                $sql="SELECT SUM(COALESCE(wccz, 0)) as hj FROM `tuqoa_xmcztjb` where `projectid`=$gcid and `sbrq`>='$startDate' and `sbrq`<='$endDate' order by sbrq desc";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $hj=$row["hj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">完成产值</h5>
                            <h2 class="card-text">¥<?=$hj?>万</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">实际成本</h5>
                            <h2 class="card-text">¥0万</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">成本控制率</h5>
                            <h2 class="card-text">0%</h2>
                        </div>
                    </div>
                </div>
            </div>
            <?php } ?>
            <div class="row mt-4">
                <?php
                $monthlyCostLabels = [];
                $monthlyCostData = [];
                $sql = "SELECT DATE_FORMAT(sbrq, '%Y-%m') as month, SUM(wccz) as total 
                        FROM tuqoa_xmcztjb 
                        WHERE projectid = $gcid AND sbrq BETWEEN '$startDate' AND '$endDate' 
                        GROUP BY DATE_FORMAT(sbrq, '%Y-%m') 
                        ORDER BY month";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $monthlyCostLabels[] = date('m月', strtotime($row['month']));
                    $monthlyCostData[] = $row['total'];
                }
                $monthlyCostLabelsJSON = json_encode($monthlyCostLabels);
                $monthlyCostDataJSON = json_encode($monthlyCostData);
                
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container monthly-cost-chart">
                                <canvas id="monthlyCostChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本构成分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-distribution-chart">
                                <canvas id="costDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度产值明细表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>上报日期</th>
                                            <th>工程计划进度</th>
                                            <th>工程实际进度</th>
                                            <th>进度偏差原因</th>
                                            <th>完成率（%）</th>
                                            <th>本月完成产值（万元）</th>
                                            <th>本年累计完成产值（万元）</th>
                                            <th>自合同开始累计完成产值（万元）</th>
                                        </tr>
                                    </thead>
                                    <?
                                    $sql="SELECT * FROM `tuqoa_xmcztjb` where `projectid`=$gcid and `sbrq`>='$startDate' and `sbrq`<='$endDate' order by sbrq desc LIMIT 12";
                                    $result = mysqli_query($link, $sql);
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        //$hj=$row["hj"];
                                    ?>
                                    <tbody>
                                        <tr>
                                            <td><?=$row["project"]?></td>
                                            <td><?=$row["sbrq"]?></td>
                                            <td><?=$row["jhjd"]?></td>
                                            <td><?=$row["sjjd"]?></td>
                                            <td><?=$row["pcyy"]?></td>
                                            <td><?=$row["wcl"]?>%</td>
                                            <td><?=$row["wccz"]?></td>
                                            <td><?=$row["bnljcz"]?></td>
                                            <td><?=$row["ljcz"]?></td>
                                        </tr>
                                    </tbody>
                                    <?php } ?>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本控制率趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-control-chart">
                                <canvas id="costControlChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本构成比例</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-ratio-chart">
                                <canvas id="costRatioChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目详细信息</h5>
                        </div>
                        <div class="card-body">
                            <ul class="nav nav-tabs" id="projectDetailTabs" role="tablist" style="height: 800px;">
                                <iframe src="/task.php?a=p&num=gcproject&mid=<?=$gcid?>" width="100%" style="height: 600px;"></iframe>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本趋势分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-trend-chart">
                                <canvas id="costTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本汇总分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-summary-chart">
                                <canvas id="costSummaryChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
        // 从PHP传递数据到JavaScript
            const monthlyCostLabels = <?php echo $monthlyCostLabelsJSON ?>;
            const monthlyCostData = <?php echo $monthlyCostDataJSON ?>;
            
            // 初始化图表
            initCharts(monthlyCostLabels, monthlyCostData);
            
            // 更新最后更新时间
            updateLastUpdateTime();
        });
        
        // 更新最后更新时间
        
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' + 
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                                 String(now.getDate()).padStart(2, '0') + ' ' + 
                                 String(now.getHours()).padStart(2, '0') + ':' + 
                                 String(now.getMinutes()).padStart(2, '0') + ':' + 
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }
        
        // 初始化图表
        function initCharts(labels, data) {
        // 月度成本趋势图表
            const monthlyCostCtx = document.getElementById('monthlyCostChart').getContext('2d');
            new Chart(monthlyCostCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: '完成产值',
                            data: data,
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '产值 (万元)'
                            }
                        }
                    }
                }
            });

            // 成本构成分布图表
            const costDistributionCtx = document.getElementById('costDistributionChart').getContext('2d');
            new Chart(costDistributionCtx, {
                type: 'pie',
                data: {
                    labels: ['工资', '材料', '设备', '差旅', '其他'],
                    datasets: [{
                        data: [40, 30, 20, 5, 5],
                        backgroundColor: ['#2196f3', '#4caf50', '#ff9800', '#9c27b0', '#607d8b']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 成本控制率趋势图表
            const costControlCtx = document.getElementById('costControlChart').getContext('2d');
            new Chart(costControlCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '成本控制率',
                        data: [92, 93, 94, 94.5, 95, 96.2],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '控制率 (%)'
                            }
                        }
                    }
                }
            });

            // 成本构成比例图表
            const costRatioCtx = document.getElementById('costRatioChart').getContext('2d');
            new Chart(costRatioCtx, {
                type: 'bar',
                data: {
                    labels: ['市政道路工程', '住宅小区工程', '商业广场工程', '桥梁工程', '地铁工程'],
                    datasets: [
                        {
                            label: '工资占比',
                            data: [40, 40, 40, 40, 40],
                            backgroundColor: '#2196f3'
                        },
                        {
                            label: '材料占比',
                            data: [30, 30, 30, 30, 30],
                            backgroundColor: '#4caf50'
                        },
                        {
                            label: '设备占比',
                            data: [20, 20, 20, 20, 20],
                            backgroundColor: '#ff9800'
                        },
                        {
                            label: '差旅占比',
                            data: [5, 5, 5, 5, 5],
                            backgroundColor: '#9c27b0'
                        },
                        {
                            label: '其他占比',
                            data: [5, 5, 5, 5, 5],
                            backgroundColor: '#607d8b'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '占比 (%)'
                            }
                        }
                    }
                }
            });

            // 成本趋势分析图表
            const costTrendCtx = document.getElementById('costTrendChart').getContext('2d');
            new Chart(costTrendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [
                        {
                            label: '市政道路工程',
                            data: [65, 70, 75, 80, 85, 90],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '住宅小区工程',
                            data: [85, 90, 95, 100, 105, 110],
                            borderColor: '#4caf50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '商业广场工程',
                            data: [130, 135, 140, 145, 150, 155],
                            borderColor: '#ff9800',
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '桥梁工程',
                            data: [80, 85, 90, 95, 100, 105],
                            borderColor: '#9c27b0',
                            backgroundColor: 'rgba(156, 39, 176, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '地铁工程',
                            data: [220, 225, 230, 235, 240, 245],
                            borderColor: '#f44336',
                            backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            fill: false,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '月度成本 (万元)'
                            }
                        }
                    }
                }
            });

            // 成本汇总分析图表
            const costSummaryCtx = document.getElementById('costSummaryChart').getContext('2d');
            new Chart(costSummaryCtx, {
                type: 'bar',
                data: {
                    labels: ['市政道路工程', '住宅小区工程', '商业广场工程', '桥梁工程', '地铁工程'],
                    datasets: [
                        {
                            label: '计划成本',
                            data: [90, 110, 155, 105, 245],
                            backgroundColor: '#2196f3'
                        },
                        {
                            label: '实际成本',
                            data: [85, 105, 150, 105, 255],
                            backgroundColor: '#4caf50'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '成本 (万元)'
                            }
                        }
                    }
                }
            });
        }
    </script>
<?php mysqli_close($link); ?>
</body>
</html> 