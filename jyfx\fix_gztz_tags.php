<?php
echo "<h2>批量修复 gztz.php 文件中的短标签</h2>";

$file_path = 'gztz.php';

if (!file_exists($file_path)) {
    die("文件不存在: $file_path");
}

// 读取文件内容
$content = file_get_contents($file_path);

echo "<h3>修复前检查</h3>";
$short_tags = preg_match_all('/<\?(?!php)/', $content, $matches);
$short_echo_tags = preg_match_all('/<\?=/', $content, $matches2);
echo "<p>找到短标签 &lt;? : $short_tags 个</p>";
echo "<p>找到短输出标签 &lt;?= : $short_echo_tags 个</p>";

// 备份原文件
$backup_file = $file_path . '.backup.' . date('YmdHis');
file_put_contents($backup_file, $content);
echo "<p style='color: green;'>✓ 已创建备份文件: $backup_file</p>";

// 执行替换
$replacements = 0;

// 替换 <?= 为 <?php echo
$patterns_to_replace = [
    // 表格中的变量输出
    '/<\?\=\$row\["([^"]+)"\]\?\>/' => '<?php echo $row["$1"]; ?>',
    '/<\?\=\s*substr\(\$row\["([^"]+)"\],\s*-9\)\s*\?\>/' => '<?php echo substr($row["$1"], -9); ?>',
    '/<\?\=\$([a-zA-Z_][a-zA-Z0-9_]*)\?\>/' => '<?php echo $$1; ?>',
    
    // 链接中的变量
    '/mid=<\?\=\$row\["id"\]\?\>/' => 'mid=<?php echo $row["id"]; ?>',
];

foreach ($patterns_to_replace as $pattern => $replacement) {
    $new_content = preg_replace($pattern, $replacement, $content);
    if ($new_content !== $content) {
        $count = preg_match_all($pattern, $content);
        $replacements += $count;
        $content = $new_content;
        echo "<p>✓ 替换模式: $pattern ($count 次)</p>";
    }
}

// 保存修复后的文件
file_put_contents($file_path, $content);

echo "<h3>修复后检查</h3>";
$short_tags_after = preg_match_all('/<\?(?!php)/', $content, $matches);
$short_echo_tags_after = preg_match_all('/<\?=/', $content, $matches2);
echo "<p>剩余短标签 &lt;? : $short_tags_after 个</p>";
echo "<p>剩余短输出标签 &lt;?= : $short_echo_tags_after 个</p>";

echo "<h3>语法检查</h3>";
$output = [];
$return_var = 0;
exec("php -l $file_path 2>&1", $output, $return_var);

if ($return_var === 0) {
    echo "<p style='color: green;'>✓ PHP语法检查通过</p>";
} else {
    echo "<p style='color: red;'>✗ PHP语法错误:</p>";
    echo "<pre>" . implode("\n", $output) . "</pre>";
}

echo "<h3>总结</h3>";
echo "<p><strong>总替换次数:</strong> $replacements</p>";
echo "<p><strong>备份文件:</strong> $backup_file</p>";

if ($short_tags_after == 0 && $short_echo_tags_after == 0 && $return_var === 0) {
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
    echo "<h4>🎉 修复完成！</h4>";
    echo "<p>所有短标签已成功转换为标准PHP标签，语法检查通过。</p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h4>⚠️ 需要手动处理</h4>";
    echo "<p>仍有一些短标签需要手动修复，或存在语法错误。</p>";
    echo "</div>";
}

echo "<p><a href='$file_path' target='_blank'>查看修复后的文件</a></p>";
?>
