<?php
// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>调试页面</h1>";

try {
    echo "1. 开始测试...<br>";
    
    // 测试数据库连接
    include '../config.php';
    echo "2. 数据库连接成功<br>";
    
    // 测试基本变量
    $stage = isset($_GET['stage']) ? $_GET['stage'] : 'today';
    echo "3. 阶段参数: $stage<br>";
    
    // 测试时间计算
    $start_date = '';
    $end_date = date('Y-m-d');
    
    switch ($stage) {
        case 'today':
            $start_date = date('Y-m-d');
            break;
        case 'week':
            $start_date = date('Y-m-d', strtotime('monday this week'));
            break;
        case 'month':
            $start_date = date('Y-m-01');
            break;
        case 'quarter':
            $quarter = ceil(date('n') / 3);
            $start_date = date('Y') . '-' . sprintf('%02d', ($quarter - 1) * 3 + 1) . '-01';
            break;
        case 'year':
            $start_date = date('Y-01-01');
            break;
        default:
            $start_date = date('Y-m-d');
    }
    
    echo "4. 时间范围: $start_date 到 $end_date<br>";
    
    // 测试数据库查询
    $sql = "SELECT COUNT(*) as count FROM tuqoa_xmxjcc WHERE DATE(xjrq) >= '$start_date' AND DATE(xjrq) <= '$end_date'";
    echo "5. 测试SQL: $sql<br>";
    
    $result = mysqli_query($link, $sql);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "6. 查询结果: " . $row['count'] . " 条记录<br>";
    } else {
        echo "6. 查询失败: " . mysqli_error($link) . "<br>";
    }
    
    // 测试JSON编码
    $test_array = ['测试1', '测试2', '测试3'];
    $json_test = json_encode($test_array);
    echo "7. JSON测试: $json_test<br>";
    
    echo "8. 所有测试通过！<br>";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "<br>";
    echo "文件: " . $e->getFile() . "<br>";
    echo "行号: " . $e->getLine() . "<br>";
}

echo "<hr>";
echo "<a href='ssjdgzhz.php'>返回原页面</a>";
?>
