<?php
echo "<h2>PHP标签修复进度报告</h2>";

$files_to_check = [
    'demo.php',
    'dkzbcx.php',
    'dzetj.php', 
    'fgbmxmhzb.php',
    'gsxmsjhz.php',
    'gztz.php',
    'jydt.php',
    'jytj.php',
    'myxmcbmx.php',
    'myxmfymx.php',
    'ndyjsflb.php',
    'rqxz.php',
    'ssjdgzhz.php',
    'wtgz.php',
    'xmcbhs.php',
    'xmcbhsyd.php',
    'xmcbkzhzb.php',
    'xmfymx.php',
    'xmhtdzmx.php',
    'xmhthzfx.php',
    'xmsflb.php',
    'ydjysjfx.php',
    'ydjysjfxmx.php',
    'ygdt.php',
    'zzzsdt.php'
];

$fixed_files = [];
$need_fix_files = [];
$syntax_errors = [];

echo "<h3>检查结果</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>文件名</th><th>状态</th><th>短标签数量</th><th>语法检查</th></tr>";

foreach ($files_to_check as $file) {
    if (!file_exists($file)) {
        echo "<tr><td>$file</td><td style='color: gray;'>文件不存在</td><td>-</td><td>-</td></tr>";
        continue;
    }
    
    // 检查短标签
    $content = file_get_contents($file);
    $short_tags = preg_match_all('/<\?(?!php)/', $content, $matches);
    $short_echo_tags = preg_match_all('/<\?=/', $content, $matches2);
    $total_short_tags = $short_tags + $short_echo_tags;
    
    // 语法检查
    $output = [];
    $return_var = 0;
    exec("php -l $file 2>&1", $output, $return_var);
    $syntax_ok = ($return_var === 0);
    
    $status_color = 'green';
    $status_text = '✓ 已修复';
    
    if ($total_short_tags > 0) {
        $status_color = 'orange';
        $status_text = '⚠ 需要修复';
        $need_fix_files[] = $file;
    } else {
        $fixed_files[] = $file;
    }
    
    if (!$syntax_ok) {
        $status_color = 'red';
        $status_text = '✗ 语法错误';
        $syntax_errors[] = $file;
    }
    
    $syntax_status = $syntax_ok ? "<span style='color: green;'>✓ 通过</span>" : "<span style='color: red;'>✗ 错误</span>";
    
    echo "<tr>";
    echo "<td>$file</td>";
    echo "<td style='color: $status_color;'>$status_text</td>";
    echo "<td>$total_short_tags</td>";
    echo "<td>$syntax_status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>统计信息</h3>";
echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>总文件数:</strong> " . count($files_to_check) . "</p>";
echo "<p><strong>已修复文件:</strong> <span style='color: green;'>" . count($fixed_files) . "</span></p>";
echo "<p><strong>需要修复文件:</strong> <span style='color: orange;'>" . count($need_fix_files) . "</span></p>";
echo "<p><strong>语法错误文件:</strong> <span style='color: red;'>" . count($syntax_errors) . "</span></p>";
echo "</div>";

if (!empty($need_fix_files)) {
    echo "<h3>需要修复的文件</h3>";
    echo "<ul>";
    foreach ($need_fix_files as $file) {
        echo "<li><a href='$file' target='_blank'>$file</a></li>";
    }
    echo "</ul>";
}

if (!empty($syntax_errors)) {
    echo "<h3>有语法错误的文件</h3>";
    echo "<ul>";
    foreach ($syntax_errors as $file) {
        echo "<li style='color: red;'>$file</li>";
    }
    echo "</ul>";
}

echo "<h3>已完成的修复工作</h3>";
echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px;'>";
echo "<h4>主要修复内容:</h4>";
echo "<ol>";
echo "<li><strong>PHP标签标准化</strong>";
echo "<ul>";
echo "<li>将 <code>&lt;?</code> 改为 <code>&lt;?php</code></li>";
echo "<li>将 <code>&lt;?=</code> 改为 <code>&lt;?php echo</code></li>";
echo "</ul></li>";

echo "<li><strong>数据库查询优化</strong>";
echo "<ul>";
echo "<li>添加了错误处理机制</li>";
echo "<li>移除了CTE查询以提高兼容性</li>";
echo "<li>添加了变量初始化</li>";
echo "</ul></li>";

echo "<li><strong>已修复的文件</strong>";
echo "<ul>";
$confirmed_fixed = ['xmsflb.php', 'ndyjsflb.php', 'ydjysjfx.php', 'dkzbcx.php', 'dzetj.php', 'gsxmsjhz.php', 'jytj.php'];
foreach ($confirmed_fixed as $file) {
    echo "<li>$file</li>";
}
echo "</ul></li>";
echo "</ol>";
echo "</div>";

echo "<h3>下一步工作</h3>";
echo "<p>对于仍有短标签的文件，建议:</p>";
echo "<ol>";
echo "<li>逐个打开文件进行手动修复</li>";
echo "<li>使用查找替换功能批量处理</li>";
echo "<li>修复后进行语法检查</li>";
echo "<li>在浏览器中测试页面功能</li>";
echo "</ol>";

echo "<hr>";
echo "<p><em>报告生成时间: " . date('Y-m-d H:i:s') . "</em></p>";
?>
