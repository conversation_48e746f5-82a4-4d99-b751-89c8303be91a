<?php
// 启用错误报告以便调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>月度经营数据分析 - 调试版本</h2>";

try {
    // 检查config.php文件
    $config_path = '../config.php';
    if (!file_exists($config_path)) {
        throw new Exception("config.php文件不存在: $config_path");
    }
    
    include $config_path;
    
    if (!isset($link) || !$link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<p style='color: green;'>✓ 数据库连接成功</p>";
    
    // 初始化变量
    $firstDayOfMonth = date('Y-m-01');
    $lastDayOfMonth = date('Y-m-t');
    $startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
    $endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
    
    echo "<p>查询日期范围: $startDate 到 $endDate</p>";
    
    // 初始化所有变量
    $fwfhj = 0;
    $cz = 0;
    $zgrs = 1;
    $rjcz = 0;
    $month = [];
    $fwfhj_chart = [];
    $ysjehj = [];
    $total_wccz = [];
    
    // 测试各个查询
    echo "<h3>测试数据库查询</h3>";
    
    // 1. 服务费查询
    $sql = "SELECT sum(fwf) as fwfhj FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
    $result = mysqli_query($link, $sql);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $fwfhj = $row["fwfhj"] ? $row["fwfhj"] : 0;
        echo "<p>✓ 服务费合计: $fwfhj 万元</p>";
    } else {
        echo "<p style='color: red;'>✗ 服务费查询失败: " . mysqli_error($link) . "</p>";
    }
    
    // 2. 产值查询
    $sql = "SELECT IFNULL(SUM(wccz), 0) as hj FROM `tuqoa_xmcztjb` WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'";
    $result = mysqli_query($link, $sql);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $cz = $row["hj"] ? $row["hj"] : 0;
        echo "<p>✓ 总产值: $cz 万元</p>";
    } else {
        echo "<p style='color: red;'>✗ 产值查询失败: " . mysqli_error($link) . "</p>";
    }
    
    // 3. 人员查询
    $sql = "SELECT count(*) as hj FROM `tuqoa_rydp` WHERE `status`=1 and `state`='在职' and drxm<>'工程管理部待岗人员'";
    $result = mysqli_query($link, $sql);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $zgrs = $row["hj"] > 0 ? $row["hj"] : 1;
        $rjcz = number_format($cz / $zgrs, 4);
        echo "<p>✓ 在职人员: $zgrs 人，人均产值: $rjcz 万元</p>";
    } else {
        echo "<p style='color: red;'>✗ 人员查询失败: " . mysqli_error($link) . "</p>";
    }
    
    // 4. 测试月度数据查询
    echo "<h3>测试月度数据查询</h3>";
    for ($i = 2; $i >= 0; $i--) {
        $monthStr = date('Y-m', strtotime("-$i months"));
        $month[] = date('n', strtotime("-$i months")) . "月";
        
        $sql = "SELECT IFNULL(SUM(fwf), 0) AS fwfhj FROM `tuqoa_htgl` WHERE DATE_FORMAT(qdsj, '%Y-%m') = '$monthStr'";
        $result = mysqli_query($link, $sql);
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            $monthlyFwf = (float)$row["fwfhj"];
            $fwfhj_chart[] = $monthlyFwf;
            echo "<p>✓ $monthStr 服务费: $monthlyFwf 万元</p>";
        } else {
            echo "<p style='color: red;'>✗ $monthStr 查询失败: " . mysqli_error($link) . "</p>";
        }
    }
    
    echo "<h3>变量状态</h3>";
    echo "<p>month: " . json_encode($month) . "</p>";
    echo "<p>fwfhj_chart: " . json_encode($fwfhj_chart) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>错误: " . $e->getMessage() . "</p>";
    echo "<p>错误位置: " . $e->getFile() . " 第 " . $e->getLine() . " 行</p>";
}
?>

<hr>
<p><strong>如果上面所有测试都通过，那么问题可能在于完整页面的HTML结构或JavaScript部分。</strong></p>
<p><a href="ydjysjfx.php">尝试访问完整页面</a></p>
