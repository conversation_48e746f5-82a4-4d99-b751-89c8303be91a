<?php
echo "<h2>PHP标签测试</h2>";
echo "<p>完整PHP标签工作正常</p>";

$test_var = "测试变量";
?>

<p>完整标签输出: <?php echo $test_var; ?></p>
<p>短标签输出: <?=$test_var?></p>
<p>短标签测试: <? echo "短标签"; ?></p>

<?php
echo "<h3>PHP配置信息</h3>";
echo "<p>short_open_tag: " . (ini_get('short_open_tag') ? '启用' : '禁用') . "</p>";
echo "<p>PHP版本: " . phpversion() . "</p>";
?>

<h3>测试结果</h3>
<p>如果上面的"短标签输出"和"短标签测试"显示正常，说明短标签被支持。</p>
<p>如果显示为原始代码，说明短标签被禁用，需要使用完整的PHP标签。</p>
