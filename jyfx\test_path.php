<?php
echo "<h2>路径测试</h2>";
echo "<p>当前文件路径: " . __FILE__ . "</p>";
echo "<p>当前目录: " . __DIR__ . "</p>";
echo "<p>上级目录: " . dirname(__DIR__) . "</p>";

$config_path = '../config.php';
echo "<p>尝试包含: $config_path</p>";

if (file_exists($config_path)) {
    echo "<p style='color: green;'>✓ config.php 文件存在</p>";
    include $config_path;
    if (isset($link)) {
        echo "<p style='color: green;'>✓ 数据库连接成功</p>";
    } else {
        echo "<p style='color: red;'>✗ 数据库连接失败</p>";
    }
} else {
    echo "<p style='color: red;'>✗ config.php 文件不存在</p>";
    echo "<p>尝试查找config.php文件...</p>";
    
    $possible_paths = [
        '../config.php',
        '../../config.php',
        dirname(__DIR__) . '/config.php',
        dirname(dirname(__DIR__)) . '/config.php'
    ];
    
    foreach ($possible_paths as $path) {
        if (file_exists($path)) {
            echo "<p style='color: blue;'>找到config.php: $path</p>";
        }
    }
}
?>
