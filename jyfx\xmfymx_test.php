<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目费用明细表 - 测试版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f8fafc;
        }
        .test-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #1389D3;
        }
        .error {
            background-color: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #e8f5e8;
            color: #2e7d32;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #1389D3;
            color: white;
        }
    </style>
</head>
<body>
    <h1>项目费用明细表 - 测试版</h1>
    
    <div class="test-info">
        <h3>系统测试信息</h3>
        <p><strong>当前时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        <p><strong>PHP版本:</strong> <?php echo phpversion(); ?></p>
        <p><strong>服务器信息:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? '未知'; ?></p>
    </div>

    <?php
    // 启用错误报告
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    echo "<div class='test-info'><h3>步骤1: 检查配置文件</h3>";
    
    // 检查配置文件
    $config_path = '../config.php';
    if (file_exists($config_path)) {
        echo "<p class='success'>✓ 配置文件存在: $config_path</p>";
        
        // 包含配置文件
        try {
            include $config_path;
            echo "<p class='success'>✓ 配置文件加载成功</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ 配置文件加载失败: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='error'>✗ 配置文件不存在: $config_path</p>";
    }
    echo "</div>";
    
    echo "<div class='test-info'><h3>步骤2: 检查数据库连接</h3>";
    
    // 检查数据库连接
    if (isset($link) && $link) {
        echo "<p class='success'>✓ 数据库连接变量存在</p>";
        
        // 测试简单查询
        $test_sql = "SELECT 1 as test";
        $test_result = mysqli_query($link, $test_sql);
        if ($test_result) {
            echo "<p class='success'>✓ 数据库连接正常</p>";
            
            // 检查项目表
            $project_sql = "SELECT COUNT(*) as count FROM `tuqoa_gcproject`";
            $project_result = mysqli_query($link, $project_sql);
            if ($project_result) {
                $project_row = mysqli_fetch_assoc($project_result);
                echo "<p class='success'>✓ 项目表查询成功，共有 " . $project_row['count'] . " 条记录</p>";
            } else {
                echo "<p class='error'>✗ 项目表查询失败: " . mysqli_error($link) . "</p>";
            }
        } else {
            echo "<p class='error'>✗ 数据库查询失败: " . mysqli_error($link) . "</p>";
        }
    } else {
        echo "<p class='error'>✗ 数据库连接变量不存在或为空</p>";
        
        // 尝试手动连接
        echo "<p>尝试手动连接数据库...</p>";
        $manual_link = mysqli_connect('127.0.0.1', 'root', 'root', 'mas_nxyhy_cn');
        if ($manual_link) {
            echo "<p class='success'>✓ 手动数据库连接成功</p>";
            $link = $manual_link; // 设置连接变量
        } else {
            echo "<p class='error'>✗ 手动数据库连接失败: " . mysqli_connect_error() . "</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='test-info'><h3>步骤3: 显示项目数据</h3>";
    
    // 如果数据库连接正常，显示一些项目数据
    if (isset($link) && $link) {
        $sql = "SELECT id, gcname, xmzt FROM `tuqoa_gcproject` WHERE xmzt in ('新开工项目','在建项目','完工未结算') ORDER BY id DESC LIMIT 5";
        $result = mysqli_query($link, $sql);
        
        if ($result && mysqli_num_rows($result) > 0) {
            echo "<p class='success'>✓ 项目数据查询成功</p>";
            echo "<table>";
            echo "<tr><th>项目ID</th><th>项目名称</th><th>项目状态</th></tr>";
            
            while ($row = mysqli_fetch_assoc($result)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                echo "<td>" . htmlspecialchars($row['gcname']) . "</td>";
                echo "<td>" . htmlspecialchars($row['xmzt']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>✗ 没有找到项目数据或查询失败</p>";
            if ($result === false) {
                echo "<p class='error'>SQL错误: " . mysqli_error($link) . "</p>";
            }
        }
    }
    echo "</div>";
    ?>
    
    <div class="test-info">
        <h3>测试结论</h3>
        <p>如果上面所有步骤都显示成功（✓），那么主页面应该能正常工作。</p>
        <p>如果有任何步骤失败（✗），请根据错误信息进行修复。</p>
        <p><a href="xmfymx.php">返回主页面</a></p>
    </div>
</body>
</html>
