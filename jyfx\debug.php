<?php
// 调试脚本
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>PHP错误检查</h2>";

// 检查config.php
echo "检查config.php...<br>";
if (file_exists('../config.php')) {
    echo "✓ config.php 文件存在<br>";
    include '../config.php';
    if (isset($link) && $link) {
        echo "✓ 数据库连接成功<br>";
    } else {
        echo "✗ 数据库连接失败<br>";
    }
} else {
    echo "✗ config.php 文件不存在<br>";
}

echo "<br>检查ssjdgzhz.php语法...<br>";

// 使用output buffering捕获任何输出
ob_start();
$error = false;

try {
    // 尝试包含文件但不执行HTML部分
    $content = file_get_contents('ssjdgzhz.php');
    if ($content === false) {
        echo "✗ 无法读取ssjdgzhz.php文件<br>";
    } else {
        echo "✓ ssjdgzhz.php 文件可读<br>";
        
        // 检查PHP语法
        $check = php_check_syntax('ssjdgzhz.php');
        if ($check) {
            echo "✓ PHP语法检查通过<br>";
        } else {
            echo "✗ PHP语法错误<br>";
        }
    }
} catch (Exception $e) {
    echo "✗ 错误: " . $e->getMessage() . "<br>";
    $error = true;
}

$output = ob_get_clean();
echo $output;

if (!$error) {
    echo "<br><a href='ssjdgzhz.php' target='_blank'>打开工作汇总页面</a><br>";
    echo "<a href='test_data.php' target='_blank'>查看测试数据</a><br>";
}

// 检查数据库表
echo "<br><h3>数据库表检查</h3>";
if (isset($link) && $link) {
    $tables = ['tuqoa_work', 'tuqoa_todo', 'tuqoa_jlrz', 'tuqoa_aqjc', 'tuqoa_pzjl'];
    foreach ($tables as $table) {
        $result = mysqli_query($link, "SHOW TABLES LIKE '$table'");
        if ($result && mysqli_num_rows($result) > 0) {
            echo "✓ 表 $table 存在<br>";
        } else {
            echo "✗ 表 $table 不存在<br>";
        }
    }
}

function php_check_syntax($file) {
    $output = shell_exec("php -l \"$file\" 2>&1");
    return strpos($output, 'No syntax errors') !== false;
}
?>
