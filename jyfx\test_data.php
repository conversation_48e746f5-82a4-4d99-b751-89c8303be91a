<?php
// 测试数据查询脚本
include '../config.php';

echo "<h2>数据库连接测试</h2>";
if ($link) {
    echo "✓ 数据库连接成功<br><br>";
} else {
    echo "✗ 数据库连接失败: " . mysqli_connect_error() . "<br><br>";
    exit;
}

echo "<h2>表数据统计</h2>";

// 测试各个表的数据
$tables = [
    'tuqoa_gcproject' => '工程项目',
    'tuqoa_flow_bill' => '审批流程',
    'tuqoa_work' => '工作任务',
    'tuqoa_todo' => '待办事项',
    'tuqoa_jlrz' => '监理日志',
    'tuqoa_aqjc' => '安全检查',
    'tuqoa_pzjl' => '旁站监理',
    'tuqoa_gcys' => '工程验收'
];

foreach ($tables as $table => $name) {
    $sql = "SELECT COUNT(*) as count FROM `$table`";
    $result = mysqli_query($link, $sql);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "$name ($table): " . $row['count'] . " 条记录<br>";
    } else {
        echo "$name ($table): 查询失败 - " . mysqli_error($link) . "<br>";
    }
}

echo "<br><h2>工作任务详细数据</h2>";
$sql = "SELECT id, title, type, state, startdt, enddt, optname FROM tuqoa_work ORDER BY id DESC LIMIT 5";
$result = mysqli_query($link, $sql);
if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>标题</th><th>类型</th><th>状态</th><th>开始时间</th><th>结束时间</th><th>负责人</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['title'] . "</td>";
        echo "<td>" . $row['type'] . "</td>";
        echo "<td>" . ($row['state'] == 1 ? '已完成' : '进行中') . "</td>";
        echo "<td>" . $row['startdt'] . "</td>";
        echo "<td>" . $row['enddt'] . "</td>";
        echo "<td>" . $row['optname'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "没有工作任务数据<br>";
}

echo "<br><h2>待办事项详细数据</h2>";
$sql = "SELECT id, title, status, tododt FROM tuqoa_todo ORDER BY id DESC LIMIT 5";
$result = mysqli_query($link, $sql);
if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>标题</th><th>状态</th><th>计划时间</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['title'] . "</td>";
        echo "<td>" . ($row['status'] == 1 ? '已完成' : '待处理') . "</td>";
        echo "<td>" . $row['tododt'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "没有待办事项数据<br>";
}

echo "<br><h2>统计测试</h2>";

// 测试统计逻辑
$start_date = date('Y-m-01'); // 本月开始
$end_date = date('Y-m-d');    // 今天

echo "统计时间范围: $start_date 到 $end_date<br><br>";

// 工作任务统计
$sql = "SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN state = 1 THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN state = 0 AND (enddt IS NULL OR DATE(enddt) >= CURDATE()) THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN state = 0 AND enddt IS NOT NULL AND DATE(enddt) < CURDATE() THEN 1 ELSE 0 END) as overdue
    FROM tuqoa_work 
    WHERE DATE(startdt) >= '$start_date' AND DATE(startdt) <= '$end_date'";

$result = mysqli_query($link, $sql);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "工作任务统计:<br>";
    echo "- 总数: " . $row['total'] . "<br>";
    echo "- 已完成: " . $row['completed'] . "<br>";
    echo "- 待处理: " . $row['pending'] . "<br>";
    echo "- 逾期: " . $row['overdue'] . "<br><br>";
}

// 待办事项统计
$sql = "SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN status = 0 AND DATE(tododt) >= CURDATE() THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN status = 0 AND DATE(tododt) < CURDATE() THEN 1 ELSE 0 END) as overdue
    FROM tuqoa_todo 
    WHERE DATE(tododt) >= '$start_date' AND DATE(tododt) <= '$end_date'";

$result = mysqli_query($link, $sql);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "待办事项统计:<br>";
    echo "- 总数: " . $row['total'] . "<br>";
    echo "- 已完成: " . $row['completed'] . "<br>";
    echo "- 待处理: " . $row['pending'] . "<br>";
    echo "- 逾期: " . $row['overdue'] . "<br><br>";
}

echo "<br><h2>工程项目详细数据</h2>";
$sql = "SELECT id, gcname, fzname, leixing, state, jhstartdt, jhenddt FROM tuqoa_gcproject ORDER BY id DESC LIMIT 5";
$result = mysqli_query($link, $sql);
if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>项目名称</th><th>负责人</th><th>项目类型</th><th>状态</th><th>计划开始</th><th>计划结束</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        $status_text = '';
        switch($row['state']) {
            case 0: $status_text = '未开始'; break;
            case 1: $status_text = '进行中'; break;
            case 2: $status_text = '已完成'; break;
            default: $status_text = '未知';
        }
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['gcname'] . "</td>";
        echo "<td>" . $row['fzname'] . "</td>";
        echo "<td>" . $row['leixing'] . "</td>";
        echo "<td>" . $status_text . "</td>";
        echo "<td>" . $row['jhstartdt'] . "</td>";
        echo "<td>" . $row['jhenddt'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "没有工程项目数据<br>";
}

echo "<br><h2>审批流程详细数据</h2>";
$sql = "SELECT id, sericnum, modename, uname, status, isturn, applydt FROM tuqoa_flow_bill WHERE isdel = 0 ORDER BY id DESC LIMIT 5";
$result = mysqli_query($link, $sql);
if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>单号</th><th>模块</th><th>申请人</th><th>状态</th><th>是否提交</th><th>申请日期</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        $status_text = '';
        if ($row['status'] == 1) {
            $status_text = '已审批';
        } elseif ($row['status'] == -1) {
            $status_text = '已驳回';
        } elseif ($row['isturn'] == 1) {
            $status_text = '待审批';
        } else {
            $status_text = '草稿';
        }
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['sericnum'] . "</td>";
        echo "<td>" . $row['modename'] . "</td>";
        echo "<td>" . $row['uname'] . "</td>";
        echo "<td>" . $status_text . "</td>";
        echo "<td>" . ($row['isturn'] ? '是' : '否') . "</td>";
        echo "<td>" . $row['applydt'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "没有审批流程数据<br>";
}

echo "<br><h2>项目类型统计</h2>";
$sql = "SELECT leixing, COUNT(*) as count FROM tuqoa_gcproject GROUP BY leixing ORDER BY count DESC";
$result = mysqli_query($link, $sql);
if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 50%;'>";
    echo "<tr><th>项目类型</th><th>数量</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . ($row['leixing'] ?: '未分类') . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "没有项目类型数据<br>";
}

echo "<br><h2>测试完成</h2>";
echo "<a href='ssjdgzhz.php'>返回工作汇总页面</a>";
?>
