<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题跟踪 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-pending {
            background-color: #ffeb3b;
            color: #000;
        }
        .status-resolved {
            background-color: #4caf50;
            color: #fff;
        }
        .status-in-progress {
            background-color: #2196f3;
            color: #fff;
        }
        .status-critical {
            background-color: #f44336;
            color: #fff;
        }
        .priority-icon {
            font-size: 1.2rem;
            margin-right: 0.3rem;
        }
        .priority-high {
            color: #f44336;
        }
        .priority-medium {
            color: #ff9800;
        }
        .priority-low {
            color: #4caf50;
        }
        .filter-container {
            margin-bottom: 1rem;
        }
        .chart-container {
            height: 300px;
        }
        .progress {
            height: 1.5rem;
        }
        .progress-bar {
            font-size: 0.75rem;
            font-weight: 600;
        }
        .refresh-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .auto-refresh {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .auto-refresh i {
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .issue-type-icon {
            font-size: 1.2rem;
            margin-right: 0.5rem;
        }
        .issue-type-bug {
            color: #e53935;
        }
        .issue-type-feature {
            color: #43a047;
        }
        .issue-type-improvement {
            color: #1e88e5;
        }
        .issue-type-documentation {
            color: #8e24aa;
        }
        .issue-type-other {
            color: #6d4c41;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>问题跟踪</h2>
                <div class="d-flex align-items-center">
                    <span class="refresh-time me-3">最后更新时间: <span id="last-update-time">2023-06-15 14:30:45</span></span>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="auto-refresh-toggle" checked>
                        <label class="form-check-label auto-refresh" for="auto-refresh-toggle">
                            <i class="bx bx-refresh me-1"></i> 自动刷新
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 项目选择器 -->
            <div class="date-range-container mb-4">
                <form method="post" action="">
                <div class="filter-row mb-4">
                    <div class="filter-item">
                        <label for="project-select">选择项目：</label>
                        <select id="project-select" class="form-select" style="width: auto; display: inline-block;" name="gcid">
                            <?
                            $gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
                            $sql="SELECT * FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                            $result = mysqli_query($link, $sql);
                            while ($row = mysqli_fetch_assoc($result)) {
                                $selected = ($gcid == $row["id"]) ? 'selected' : '';
                            ?>
                            <option value="<?=$row["id"]?>" <?=$selected?>><?=$row["gcname"]?></option>
                            <?
                            }
                            ?>
                        </select>
                    </div>
                    <button type="submit" id="query-btn" class="btn btn-primary">提交</button>
                    
                </div>
                </form>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">问题总数</h5>
                            <h2 class="card-text">87</h2>
                            <p class="text-primary">较上周 +5</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">已解决问题</h5>
                            <h2 class="card-text">52</h2>
                            <p class="text-success">59.8% 解决率</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">未解决问题</h5>
                            <h2 class="card-text">28</h2>
                            <p class="text-warning">32.2% 未解决</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">紧急问题</h5>
                            <h2 class="card-text">7</h2>
                            <p class="text-danger">8.0% 紧急</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">问题解决情况</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="issueResolutionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">问题类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="issueTypeDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">问题解决率趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="resolutionTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">未解决问题列表</h5>
                            <span class="badge bg-warning text-dark">28 项</span>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>问题ID</th>
                                            <th>问题类型</th>
                                            <th>项目名称</th>
                                            <th>问题描述</th>
                                            <th>优先级</th>
                                            <th>报告人</th>
                                            <th>报告日期</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr data-type="bug" data-status="in-progress">
                                            <td>ISSUE-001</td>
                                            <td>
                                                <i class="bx bx-bug work-type-icon issue-type-icon issue-type-bug"></i>
                                                缺陷
                                            </td>
                                            <td>市政道路工程</td>
                                            <td>路面裂缝修复后仍有渗水现象</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-high"></i>
                                                高
                                            </td>
                                            <td>张工</td>
                                            <td>2023-06-10</td>
                                            <td><span class="status-badge status-in-progress">处理中</span></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                        <tr data-type="feature" data-status="pending">
                                            <td>ISSUE-002</td>
                                            <td>
                                                <i class="bx bx-plus-circle work-type-icon issue-type-icon issue-type-feature"></i>
                                                功能
                                            </td>
                                            <td>市政道路工程</td>
                                            <td>需要增加道路监控系统</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-medium"></i>
                                                中
                                            </td>
                                            <td>李工</td>
                                            <td>2023-06-12</td>
                                            <td><span class="status-badge status-pending">待处理</span></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                        <tr data-type="improvement" data-status="in-progress">
                                            <td>ISSUE-003</td>
                                            <td>
                                                <i class="bx bx-up-arrow-circle work-type-icon issue-type-icon issue-type-improvement"></i>
                                                改进
                                            </td>
                                            <td>市政道路工程</td>
                                            <td>优化交通信号灯配时方案</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-medium"></i>
                                                中
                                            </td>
                                            <td>王工</td>
                                            <td>2023-06-13</td>
                                            <td><span class="status-badge status-in-progress">处理中</span></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                        <tr data-type="bug" data-status="critical">
                                            <td>ISSUE-004</td>
                                            <td>
                                                <i class="bx bx-bug work-type-icon issue-type-icon issue-type-bug"></i>
                                                缺陷
                                            </td>
                                            <td>市政道路工程</td>
                                            <td>桥梁支座出现严重变形</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-high"></i>
                                                高
                                            </td>
                                            <td>赵工</td>
                                            <td>2023-06-14</td>
                                            <td><span class="status-badge status-critical">紧急</span></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                        <tr data-type="documentation" data-status="pending">
                                            <td>ISSUE-005</td>
                                            <td>
                                                <i class="bx bx-file work-type-icon issue-type-icon issue-type-documentation"></i>
                                                文档
                                            </td>
                                            <td>市政道路工程</td>
                                            <td>施工图纸需要更新</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-low"></i>
                                                低
                                            </td>
                                            <td>钱工</td>
                                            <td>2023-06-15</td>
                                            <td><span class="status-badge status-pending">待处理</span></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">紧急问题列表</h5>
                            <span class="badge bg-danger">7 项</span>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>问题ID</th>
                                            <th>问题类型</th>
                                            <th>项目名称</th>
                                            <th>问题描述</th>
                                            <th>优先级</th>
                                            <th>报告人</th>
                                            <th>报告日期</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr data-type="bug" data-status="critical">
                                            <td>ISSUE-004</td>
                                            <td>
                                                <i class="bx bx-bug work-type-icon issue-type-icon issue-type-bug"></i>
                                                缺陷
                                            </td>
                                            <td>市政道路工程</td>
                                            <td>桥梁支座出现严重变形</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-high"></i>
                                                高
                                            </td>
                                            <td>赵工</td>
                                            <td>2023-06-14</td>
                                            <td><span class="status-badge status-critical">紧急</span></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                        <tr data-type="bug" data-status="critical">
                                            <td>ISSUE-006</td>
                                            <td>
                                                <i class="bx bx-bug work-type-icon issue-type-icon issue-type-bug"></i>
                                                缺陷
                                            </td>
                                            <td>住宅小区工程</td>
                                            <td>地下室防水层出现大面积渗漏</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-high"></i>
                                                高
                                            </td>
                                            <td>孙工</td>
                                            <td>2023-06-13</td>
                                            <td><span class="status-badge status-critical">紧急</span></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                        <tr data-type="bug" data-status="critical">
                                            <td>ISSUE-007</td>
                                            <td>
                                                <i class="bx bx-bug work-type-icon issue-type-icon issue-type-bug"></i>
                                                缺陷
                                            </td>
                                            <td>商业广场工程</td>
                                            <td>消防系统故障，无法正常启动</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-high"></i>
                                                高
                                            </td>
                                            <td>周工</td>
                                            <td>2023-06-12</td>
                                            <td><span class="status-badge status-critical">紧急</span></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">已解决问题列表</h5>
                            <span class="badge bg-success">52 项</span>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>问题ID</th>
                                            <th>问题类型</th>
                                            <th>项目名称</th>
                                            <th>问题描述</th>
                                            <th>优先级</th>
                                            <th>报告人</th>
                                            <th>报告日期</th>
                                            <th>解决日期</th>
                                            <th>解决人</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr data-type="bug" data-status="resolved">
                                            <td>ISSUE-008</td>
                                            <td>
                                                <i class="bx bx-bug work-type-icon issue-type-icon issue-type-bug"></i>
                                                缺陷
                                            </td>
                                            <td>市政道路工程</td>
                                            <td>路灯不亮</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-medium"></i>
                                                中
                                            </td>
                                            <td>吴工</td>
                                            <td>2023-06-05</td>
                                            <td>2023-06-07</td>
                                            <td>郑工</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                        <tr data-type="feature" data-status="resolved">
                                            <td>ISSUE-009</td>
                                            <td>
                                                <i class="bx bx-plus-circle work-type-icon issue-type-icon issue-type-feature"></i>
                                                功能
                                            </td>
                                            <td>住宅小区工程</td>
                                            <td>增加小区门禁系统</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-medium"></i>
                                                中
                                            </td>
                                            <td>王工</td>
                                            <td>2023-06-06</td>
                                            <td>2023-06-10</td>
                                            <td>李工</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                        <tr data-type="improvement" data-status="resolved">
                                            <td>ISSUE-010</td>
                                            <td>
                                                <i class="bx bx-up-arrow-circle work-type-icon issue-type-icon issue-type-improvement"></i>
                                                改进
                                            </td>
                                            <td>商业广场工程</td>
                                            <td>优化停车场标识系统</td>
                                            <td>
                                                <i class="bx bx-error-circle priority-icon priority-low"></i>
                                                低
                                            </td>
                                            <td>赵工</td>
                                            <td>2023-06-07</td>
                                            <td>2023-06-09</td>
                                            <td>钱工</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary">详情</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">编辑</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">问题解决率统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>问题总数</th>
                                            <th>已解决</th>
                                            <th>未解决</th>
                                            <th>紧急</th>
                                            <th>解决率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>市政道路工程</td>
                                            <td>35</td>
                                            <td>22</td>
                                            <td>10</td>
                                            <td>3</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 62.9%;" aria-valuenow="62.9" aria-valuemin="0" aria-valuemax="100">62.9%</div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>住宅小区工程</td>
                                            <td>28</td>
                                            <td>18</td>
                                            <td>8</td>
                                            <td>2</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 64.3%;" aria-valuenow="64.3" aria-valuemin="0" aria-valuemax="100">64.3%</div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>商业广场工程</td>
                                            <td>22</td>
                                            <td>12</td>
                                            <td>8</td>
                                            <td>2</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 54.5%;" aria-valuenow="54.5" aria-valuemin="0" aria-valuemax="100">54.5%</div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>桥梁工程</td>
                                            <td>15</td>
                                            <td>8</td>
                                            <td>5</td>
                                            <td>2</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 53.3%;" aria-valuenow="53.3" aria-valuemin="0" aria-valuemax="100">53.3%</div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>地铁工程</td>
                                            <td>12</td>
                                            <td>7</td>
                                            <td>4</td>
                                            <td>1</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 58.3%;" aria-valuenow="58.3" aria-valuemin="0" aria-valuemax="100">58.3%</div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>合计</td>
                                            <td>87</td>
                                            <td>52</td>
                                            <td>28</td>
                                            <td>7</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 59.8%;" aria-valuenow="59.8" aria-valuemin="0" aria-valuemax="100">59.8%</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 项目选择器变化事件
            document.getElementById('project-select').addEventListener('change', function() {
                const selectedProject = this.value;
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询项目:', selectedProject);
            });
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const selectedProject = document.getElementById('project-select').value;
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询项目:', selectedProject);
                // 模拟数据刷新
                updateLastUpdateTime();
                //alert('已更新数据，项目: ' + selectedProject);
            });
            
            // 问题类型筛选按钮点击事件
            document.querySelectorAll('.filter-container .btn-group:first-child .btn').forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    document.querySelectorAll('.filter-container .btn-group:first-child .btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    // 为当前按钮添加active类
                    this.classList.add('active');
                    
                    const filter = this.getAttribute('data-filter');
                    
                    // 筛选表格行
                    const rows = document.querySelectorAll('tbody tr');
                    rows.forEach(row => {
                        if (filter === 'all' || row.getAttribute('data-type') === filter) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            });
            
            // 问题状态筛选按钮点击事件
            document.querySelectorAll('.filter-container .btn-group:last-child .btn').forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    document.querySelectorAll('.filter-container .btn-group:last-child .btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    // 为当前按钮添加active类
                    this.classList.add('active');
                    
                    const status = this.getAttribute('data-status');
                    
                    // 筛选表格行
                    const rows = document.querySelectorAll('tbody tr');
                    rows.forEach(row => {
                        if (status === 'all' || row.getAttribute('data-status') === status) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            });
            
            // 自动刷新开关事件
            document.getElementById('auto-refresh-toggle').addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });
            
            // 初始化图表
            initCharts();
            
            // 启动自动刷新
            startAutoRefresh();
        });
        
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' + 
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                                 String(now.getDate()).padStart(2, '0') + ' ' + 
                                 String(now.getHours()).padStart(2, '0') + ':' + 
                                 String(now.getMinutes()).padStart(2, '0') + ':' + 
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }
        
        // 自动刷新定时器
        let autoRefreshTimer;
        
        // 启动自动刷新
        function startAutoRefresh() {
            // 每60秒刷新一次
            autoRefreshTimer = setInterval(function() {
                // 这里可以添加刷新逻辑，例如AJAX请求获取最新数据
                console.log('自动刷新数据');
                updateLastUpdateTime();
                // 模拟数据刷新
                // 实际应用中应该替换为真实的数据刷新逻辑
            }, 60000);
        }
        
        // 停止自动刷新
        function stopAutoRefresh() {
            clearInterval(autoRefreshTimer);
        }
        
        // 初始化图表
        function initCharts() {
            // 问题解决情况图表
            const issueResolutionCtx = document.getElementById('issueResolutionChart').getContext('2d');
            new Chart(issueResolutionCtx, {
                type: 'pie',
                data: {
                    labels: ['已解决', '未解决', '紧急'],
                    datasets: [{
                        data: [52, 28, 7],
                        backgroundColor: ['#4caf50', '#ffeb3b', '#f44336']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 问题类型分布图表
            const issueTypeDistributionCtx = document.getElementById('issueTypeDistributionChart').getContext('2d');
            new Chart(issueTypeDistributionCtx, {
                type: 'pie',
                data: {
                    labels: ['缺陷', '功能', '改进', '文档', '其他'],
                    datasets: [{
                        data: [35, 18, 15, 12, 7],
                        backgroundColor: ['#e53935', '#43a047', '#1e88e5', '#8e24aa', '#6d4c41']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 问题解决率趋势图表
            const resolutionTrendCtx = document.getElementById('resolutionTrendChart').getContext('2d');
            new Chart(resolutionTrendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '解决率',
                        data: [45, 48, 52, 55, 58, 59.8],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '解决率 (%)'
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 