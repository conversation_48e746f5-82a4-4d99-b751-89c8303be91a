<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目成本核算 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        <?php
        // 获取主题色
        $maincolor = getconfig('apptheme','#1389D3');
        $maincolora = c('image')->colorTorgb($maincolor);
        $maincolors = $maincolora[0].','.$maincolora[1].','.$maincolora[2];
        ?>
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }
        .card-header {
            background-color: <?php echo $maincolor; ?>;
            color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }
        .card-header h5,
        .card-header .card-title {
            color: white !important;
            margin: 0;
        }
        .btn-primary {
            background-color: <?php echo $maincolor; ?>;
            border-color: <?php echo $maincolor; ?>;
        }
        .btn-primary:hover {
            background-color: rgba(<?php echo $maincolors; ?>, 0.8);
            border-color: rgba(<?php echo $maincolors; ?>, 0.8);
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-normal {
            background-color: #4caf50;
            color: #fff;
        }
        .status-warning {
            background-color: #ff9800;
            color: #fff;
        }
        .status-danger {
            background-color: #f44336;
            color: #fff;
        }

        .status-success {
            background-color: #4caf50;
            color: #fff;
        }
        .filter-container {
            margin-bottom: 1rem;
        }
        .chart-container {
            height: 300px;
        }
        .progress {
            height: 1.5rem;
        }
        .progress-bar {
            font-size: 0.75rem;
            font-weight: 600;
        }
        .refresh-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .cost-trend-up {
            color: #f44336;
        }
        .cost-trend-down {
            color: #4caf50;
        }
        .cost-trend-stable {
            color: #2196f3;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        .monthly-cost-chart {
            height: 250px;
        }
        .cost-distribution-chart {
            height: 250px;
        }
        .cost-trend-chart {
            height: 250px;
        }
        .cost-control-chart {
            height: 250px;
        }
        .cost-ratio-chart {
            height: 250px;
        }
        .cost-summary-chart {
            height: 250px;
        }
        .filter-row {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .filter-item {
            margin-right: 1rem;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>项目成本核算</h2>
                <div class="d-flex align-items-center">
                    <span class="refresh-time me-3">最后更新时间: <span id="last-update-time">2023-06-15 14:30:45</span></span>
                </div>
            </div>
            
            <!-- 项目选择和年份选择放在同一行 -->
            <form method="post" action="">
            <div class="filter-row mb-4">
                <div class="filter-item">
                    <label for="project-select">选择项目：</label>
                    <select id="project-select" class="form-select" style="width: auto; display: inline-block;" name="gcid">
                        <?
                        $gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
                        $sql="SELECT * FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                        $result = mysqli_query($link, $sql);
                            while ($row = mysqli_fetch_assoc($result)) {
                                $selected = ($gcid == $row["id"]) ? 'selected' : '';
                        ?>
                        <option value="<?=$row["id"]?>" <?=$selected?>><?=$row["gcname"]?></option>
                        <?
                            }
                        ?>
                    </select>
                </div>
                 <div class="filter-item">
                    <label for="year-select">选择年份：</label>
                    <select id="year-select" class="form-select" style="width: auto; display: inline-block;">
                        <option value="2023">2023年</option>
                        <option value="2022">2022年</option>
                        <option value="2021">2021年</option>
                    </select>
                </div>
                <button type="submit" id="query-btn" class="btn btn-primary">提交</button>
                
            </div>
            </form>
            
            
            <div class="row">
                <?php
                // 查询合同总额
                $sql="SELECT COALESCE(SUM(zaojia), 0) as htzje FROM `tuqoa_gcproject` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                $htzje = 0;
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $htzje = round($row["htzje"], 2);
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">合同总额</h5>
                            <h2 class="card-text">¥<?php echo $htzje; ?>万</h2>
                            <p class="text-primary">当期合同总额</p>
                        </div>
                    </div>
                </div>

                <?php
                // 查询收费总额
                $sql="SELECT COALESCE(SUM(fwf), 0) as sfzje FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                $sfzje = 0;
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $sfzje = round($row["sfzje"], 2);
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">收费总额</h5>
                            <h2 class="card-text">¥<?php echo $sfzje; ?>万</h2>
                            <p class="text-success">当期收费总额</p>
                        </div>
                    </div>
                </div>

                <?php
                // 查询实际成本总额
                $sql="SELECT COALESCE(SUM(wccz), 0) as sjcbzje FROM `tuqoa_xmcztjb` WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'";
                $result = mysqli_query($link, $sql);
                $sjcbzje = 0;
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $sjcbzje = round($row["sjcbzje"], 2);
                    }
                } else {
                    echo "查询错误: " . mysqli_error($link);
                }

                // 计算成本控制率
                $cbkzl = ($sfzje > 0) ? round(($sjcbzje / $sfzje) * 100, 1) : 0;
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">实际成本总额</h5>
                            <h2 class="card-text">¥<?php echo $sjcbzje; ?>万</h2>
                            <p class="text-warning">当期实际成本</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">成本控制率</h5>
                            <h2 class="card-text"><?php echo $cbkzl; ?>%</h2>
                            <p class="text-<?php echo ($cbkzl <= 80) ? 'success' : 'warning'; ?>">
                                <?php echo ($cbkzl <= 80) ? '控制良好' : '需要关注'; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container monthly-cost-chart">
                                <canvas id="monthlyCostChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本构成分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-distribution-chart">
                                <canvas id="costDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目成本明细表</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>项目识别号</th>
                                            <th>财务识别号</th>
                                            <th>合同额(万)</th>
                                            <th>收费额(万)</th>
                                            <th>成本系数</th>
                                            <th>计划成本(万)</th>
                                            <th>实际成本(万)</th>
                                            <th>工资总额(万)</th>
                                            <th>工资占比</th>
                                            <th>报销比例</th>
                                            <th>成本控制率</th>
                                            <th>月平均计划成本(万)</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql="SELECT * FROM `tuqoa_gcproject` WHERE xmzt in ('新开工项目','在建项目','完工未结算') order by id desc";
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                // 计算成本相关数据
                                                $htje = $row["zaojia"]; // 合同额
                                                $sfje = 0; // 收费额
                                                $jhcb = 0; // 计划成本
                                                $sjcb = 0; // 实际成本
                                                $gzze = 0; // 工资总额

                                                // 查询收费额
                                                $sql1="SELECT COALESCE(SUM(fwf), 0) as sfje FROM `tuqoa_htgl` WHERE `gcid`=".$row["id"];
                                                $result1 = mysqli_query($link, $sql1);
                                                if ($result1) {
                                                    $row1 = mysqli_fetch_assoc($result1);
                                                    $sfje = $row1["sfje"];
                                                }

                                                // 查询实际成本
                                                $sql2="SELECT COALESCE(SUM(wccz), 0) as sjcb FROM `tuqoa_xmcztjb` WHERE `projectid`=".$row["id"];
                                                $result2 = mysqli_query($link, $sql2);
                                                if ($result2) {
                                                    $row2 = mysqli_fetch_assoc($result2);
                                                    $sjcb = $row2["sjcb"];
                                                }

                                                // 计算各种比率
                                                $cbxs = ($sfje > 0) ? round($sjcb / $sfje, 2) : 0; // 成本系数
                                                $jhcb = $sfje * 0.8; // 假设计划成本为收费额的80%
                                                $gzzb = ($sjcb > 0) ? round(($gzze / $sjcb) * 100, 1) : 0; // 工资占比
                                                $cbkzl = ($jhcb > 0) ? round(($sjcb / $jhcb) * 100, 1) : 0; // 成本控制率
                                                $ypjjhcb = $jhcb / 12; // 月平均计划成本

                                                // 状态判断
                                                $status = "正常";
                                                $statusClass = "status-normal";
                                                if ($cbkzl > 100) {
                                                    $status = "超支";
                                                    $statusClass = "status-danger";
                                                } elseif ($cbkzl > 90) {
                                                    $status = "预警";
                                                    $statusClass = "status-warning";
                                                }
                                        ?>
                                        <tr>
                                            <td><?php echo $row["gcname"]; ?></td>
                                            <td>PRJ-<?php echo $row["id"]; ?></td>
                                            <td>FIN-<?php echo $row["id"]; ?></td>
                                            <td><?php echo number_format($htje, 2); ?></td>
                                            <td><?php echo number_format($sfje, 2); ?></td>
                                            <td><?php echo $cbxs; ?></td>
                                            <td><?php echo number_format($jhcb, 2); ?></td>
                                            <td><?php echo number_format($sjcb, 2); ?></td>
                                            <td><?php echo number_format($gzze, 2); ?></td>
                                            <td><?php echo $gzzb; ?>%</td>
                                            <td>--</td>
                                            <td><?php echo $cbkzl; ?>%</td>
                                            <td><?php echo number_format($ypjjhcb, 2); ?></td>
                                            <td><span class="status-badge <?php echo $statusClass; ?>"><?php echo $status; ?></span></td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                            echo "<tr><td colspan='14'>查询错误: " . mysqli_error($link) . "</td></tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本控制率趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-control-chart">
                                <canvas id="costControlChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本构成比例</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-ratio-chart">
                                <canvas id="costRatioChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度成本明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>项目识别号</th>
                                            <th>1月</th>
                                            <th>2月</th>
                                            <th>3月</th>
                                            <th>4月</th>
                                            <th>5月</th>
                                            <th>6月</th>
                                            <th>7月</th>
                                            <th>8月</th>
                                            <th>9月</th>
                                            <th>10月</th>
                                            <th>11月</th>
                                            <th>12月</th>
                                            <th>月平均</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!--
                                        <tr>
                                            <td>市政道路工程</td>
                                            <td>PRJ-2023-001</td>
                                            <td>65</td>
                                            <td>70</td>
                                            <td>75</td>
                                            <td>80</td>
                                            <td>85</td>
                                            <td>90</td>
                                            <td>95</td>
                                            <td>100</td>
                                            <td>105</td>
                                            <td>110</td>
                                            <td>115</td>
                                            <td>120</td>
                                            <td>90</td>
                                        </tr>
                                        -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本控制率统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>计划成本(万)</th>
                                            <th>实际成本(万)</th>
                                            <th>差异(万)</th>
                                            <th>成本控制率</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!--
                                        <tr>
                                            <td>市政道路工程</td>
                                            <td>960</td>
                                            <td>720</td>
                                            <td class="cost-trend-down">-240</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">75%</div>
                                                </div>
                                            </td>
                                            <td><span class="status-badge status-normal">正常</span></td>
                                        </tr>
                                        <tr>
                                            <td>合计</td>
                                            <td>8,512</td>
                                            <td>8,028.8</td>
                                            <td class="cost-trend-down">-483.2</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">75%</div>
                                                </div>
                                            </td>
                                            <td><span class="status-badge status-normal">正常</span></td>
                                        </tr>
                                        -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本构成分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>工资总额(万)</th>
                                            <th>材料费用(万)</th>
                                            <th>设备费用(万)</th>
                                            <th>差旅费用(万)</th>
                                            <th>其他费用(万)</th>
                                            <th>工资占比</th>
                                            <th>材料占比</th>
                                            <th>设备占比</th>
                                            <th>差旅占比</th>
                                            <th>其他占比</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!--
                                        <tr>
                                            <td>市政道路工程</td>
                                            <td>288</td>
                                            <td>216</td>
                                            <td>144</td>
                                            <td>36</td>
                                            <td>36</td>
                                            <td>40%</td>
                                            <td>30%</td>
                                            <td>20%</td>
                                            <td>5%</td>
                                            <td>5%</td>
                                        </tr>
                                        -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本趋势分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-trend-chart">
                                <canvas id="costTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本汇总分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container cost-summary-chart">
                                <canvas id="costSummaryChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <?php
    // 生成图表数据
    $monthlyLabels = [];
    $monthlyCostData = [];
    $monthlyPlanData = [];

    // 生成最近12个月的数据
    for ($i = 11; $i >= 0; $i--) {
        $month = date('Y-m', strtotime("-$i months"));
        $monthLabel = date('n', strtotime("-$i months")) . '月';
        $monthlyLabels[] = $monthLabel;

        // 查询该月实际成本
        $sql_cost = "SELECT COALESCE(SUM(wccz), 0) as monthly_cost FROM tuqoa_xmcztjb WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$month'";
        $result_cost = mysqli_query($link, $sql_cost);
        $monthly_cost = 0;
        if ($result_cost) {
            $row_cost = mysqli_fetch_assoc($result_cost);
            $monthly_cost = (float)$row_cost['monthly_cost'];
        }
        $monthlyCostData[] = $monthly_cost;

        // 计划成本（假设为实际成本的1.2倍）
        $monthlyPlanData[] = $monthly_cost * 1.2;
    }

    // 成本构成数据
    $costComposition = [
        '工资' => 0,
        '材料' => 0,
        '设备' => 0,
        '差旅' => 0,
        '其他' => 0
    ];

    // 查询各类成本数据（这里简化处理，实际应该根据具体的成本分类字段查询）
    $sql_composition = "SELECT
        COALESCE(SUM(wccz), 0) * 0.4 as salary_cost,
        COALESCE(SUM(wccz), 0) * 0.3 as material_cost,
        COALESCE(SUM(wccz), 0) * 0.2 as equipment_cost,
        COALESCE(SUM(wccz), 0) * 0.05 as travel_cost,
        COALESCE(SUM(wccz), 0) * 0.05 as other_cost
        FROM tuqoa_xmcztjb WHERE sbrq >= '$startDate' AND sbrq <= '$endDate'";
    $result_composition = mysqli_query($link, $sql_composition);
    if ($result_composition) {
        $row_comp = mysqli_fetch_assoc($result_composition);
        $costComposition['工资'] = (float)$row_comp['salary_cost'];
        $costComposition['材料'] = (float)$row_comp['material_cost'];
        $costComposition['设备'] = (float)$row_comp['equipment_cost'];
        $costComposition['差旅'] = (float)$row_comp['travel_cost'];
        $costComposition['其他'] = (float)$row_comp['other_cost'];
    }
    ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 项目选择器变化事件
            document.getElementById('project-select').addEventListener('change', function() {
                const selectedProject = this.value;
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询项目:', selectedProject);
            });
            
            // 年份选择器变化事件
            document.getElementById('year-select').addEventListener('change', function() {
                const selectedYear = this.value;
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询年份:', selectedYear);
            });
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const selectedProject = document.getElementById('project-select').value;
                const selectedYear = document.getElementById('year-select').value;
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询项目:', selectedProject, '年份:', selectedYear);
                // 模拟数据刷新
                updateLastUpdateTime();
                //alert('已更新数据，项目: ' + selectedProject + ', 年份: ' + selectedYear);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const formattedDate = now.getFullYear() + '-' + 
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                                 String(now.getDate()).padStart(2, '0') + ' ' + 
                                 String(now.getHours()).padStart(2, '0') + ':' + 
                                 String(now.getMinutes()).padStart(2, '0') + ':' + 
                                 String(now.getSeconds()).padStart(2, '0');
            document.getElementById('last-update-time').textContent = formattedDate;
        }
        
        // 初始化图表
        function initCharts() {
            // 月度成本趋势图表
            const monthlyCostCtx = document.getElementById('monthlyCostChart').getContext('2d');
            new Chart(monthlyCostCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($monthlyLabels); ?>,
                    datasets: [
                        {
                            label: '计划成本',
                            data: <?php echo json_encode($monthlyPlanData); ?>,
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: '实际成本',
                            data: <?php echo json_encode($monthlyCostData); ?>,
                            borderColor: '#4caf50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '成本 (万元)'
                            }
                        }
                    }
                }
            });

            // 成本构成分布图表
            const costDistributionCtx = document.getElementById('costDistributionChart').getContext('2d');
            new Chart(costDistributionCtx, {
                type: 'pie',
                data: {
                    labels: <?php echo json_encode(array_keys($costComposition)); ?>,
                    datasets: [{
                        data: <?php echo json_encode(array_values($costComposition)); ?>,
                        backgroundColor: ['#2196f3', '#4caf50', '#ff9800', '#9c27b0', '#607d8b']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 成本控制率趋势图表
            const costControlCtx = document.getElementById('costControlChart').getContext('2d');
            new Chart(costControlCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '成本控制率',
                        data: [70, 72, 73, 74, 74.5, 75],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '控制率 (%)'
                            }
                        }
                    }
                }
            });

            // 成本构成比例图表
            const costRatioCtx = document.getElementById('costRatioChart').getContext('2d');
            new Chart(costRatioCtx, {
                type: 'bar',
                data: {
                    labels: ['市政道路工程', '住宅小区工程', '商业广场工程', '桥梁工程', '地铁工程'],
                    datasets: [
                        {
                            label: '工资占比',
                            data: [40, 40, 40, 40, 40],
                            backgroundColor: '#2196f3'
                        },
                        {
                            label: '材料占比',
                            data: [30, 30, 30, 30, 30],
                            backgroundColor: '#4caf50'
                        },
                        {
                            label: '设备占比',
                            data: [20, 20, 20, 20, 20],
                            backgroundColor: '#ff9800'
                        },
                        {
                            label: '差旅占比',
                            data: [5, 5, 5, 5, 5],
                            backgroundColor: '#9c27b0'
                        },
                        {
                            label: '其他占比',
                            data: [5, 5, 5, 5, 5],
                            backgroundColor: '#607d8b'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '占比 (%)'
                            }
                        }
                    }
                }
            });

            // 成本趋势分析图表
            const costTrendCtx = document.getElementById('costTrendChart').getContext('2d');
            new Chart(costTrendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [
                        {
                            label: '市政道路工程',
                            data: [65, 70, 75, 80, 85, 90, 95, 100, 105, 110, 115, 120],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '住宅小区工程',
                            data: [85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140],
                            borderColor: '#4caf50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '商业广场工程',
                            data: [130, 135, 140, 145, 150, 155, 160, 165, 170, 175, 180, 185],
                            borderColor: '#ff9800',
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '桥梁工程',
                            data: [80, 85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 135],
                            borderColor: '#9c27b0',
                            backgroundColor: 'rgba(156, 39, 176, 0.1)',
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '地铁工程',
                            data: [220, 225, 230, 235, 240, 245, 250, 255, 260, 265, 270, 275],
                            borderColor: '#f44336',
                            backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            fill: false,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '月度成本 (万元)'
                            }
                        }
                    }
                }
            });

            // 成本汇总分析图表
            const costSummaryCtx = document.getElementById('costSummaryChart').getContext('2d');
            new Chart(costSummaryCtx, {
                type: 'bar',
                data: {
                    labels: ['市政道路工程', '住宅小区工程', '商业广场工程', '桥梁工程', '地铁工程'],
                    datasets: [
                        {
                            label: '计划成本',
                            data: [960, 1280, 1920, 1152, 3200],
                            backgroundColor: '#2196f3'
                        },
                        {
                            label: '实际成本',
                            data: [720, 1024, 1728, 1036.8, 3520],
                            backgroundColor: '#4caf50'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '成本 (万元)'
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 