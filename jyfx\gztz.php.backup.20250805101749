<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-d');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;

// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作台账 - 公司数据总览系统</title>
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-pending {
            background-color: #ffeb3b;
            color: #000;
        }
        .status-completed {
            background-color: #4caf50;
            color: #fff;
        }
        .status-in-progress {
            background-color: #2196f3;
            color: #fff;
        }
        .status-cancelled {
            background-color: #f44336;
            color: #fff;
        }
        .work-type-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
        }
        .work-type-inspection {
            color: #1e88e5;
        }
        .work-type-acceptance {
            color: #43a047;
        }
        .work-type-sampling {
            color: #e53935;
        }
        .work-type-supervision {
            color: #fb8c00;
        }
        .work-type-verification {
            color: #8e24aa;
        }
        .work-type-testing {
            color: #00acc1;
        }
        .work-type-meeting {
            color: #6d4c41;
        }
        .filter-container {
            margin-bottom: 1rem;
        }
        .chart-container {
            height: 300px;
            position: relative;
        }
        .date-range-container {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">工作台账</h2>
            
            <!-- 日期选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <button type="submit" id="query-btn" class="btn btn-primary">提交</button>
                    </div>
                </form>
            </div>
            <?php

            // 获取各类工作数量
            $sql="SELECT count(*) sl FROM `tuqoa_jlrz` WHERE `kssj`='$startDate'";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl=$row["sl"];
                $jlrz=$row["sl"];
            }
            
            $sql="SELECT count(*) sl FROM `tuqoa_aqrz` WHERE `kssj`='$startDate'";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl+=$row["sl"];
                $aqrz=$row["sl"];
            }
            
            
            $sql="SELECT count(*) sl FROM `tuqoa_xmxjcc` WHERE `xjrq`='$startDate' ";//order by id desc LIMIT 1
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl+=$row["sl"];
                $xmxjcc=$row["sl"];
            }
            
            
            $sql="SELECT count(*) sl FROM `tuqoa_pzjl` WHERE `kssj` like '$startDate%'";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl+=$row["sl"];
                $pzjl=$row["sl"];
            }
            
            $sql="SELECT count(*) sl FROM `tuqoa_zxjc` WHERE `jcsj`='$startDate'";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl+=$row["sl"];
                $zxjc=$row["sl"];
            }
            
            $sql="SELECT count(*) sl FROM `tuqoa_aqjc` WHERE `jcsj`='$startDate'";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl+=$row["sl"];
                $aqjc=$row["sl"];
            }
            
            $sql="SELECT count(*) sl FROM `tuqoa_gcys` WHERE `yssj` like '$startDate%'";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl+=$row["sl"];
                $gcys=$row["sl"];
            }
            
            $sql="SELECT count(*) sl FROM `tuqoa_jcys` WHERE `jcsj`='$startDate'";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl+=$row["sl"];
                $jcys=$row["sl"];
            }
            
            $sql="SELECT count(*) sl FROM `tuqoa_xmfw` WHERE `fwsj`='$startDate'";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl+=$row["sl"];
                $xmfw=$row["sl"];
            }
            
            $sql="SELECT count(*) sl FROM `tuqoa_hyjy` WHERE `kssj`='$startDate'";
            $result = mysqli_query($link, $sql);
            while ($row = mysqli_fetch_assoc($result)) {
                $sl+=$row["sl"];
                $hyjl=$row["sl"];
            }
            
            $workTypeData = [
                '监理日志' => $jlrz,
                '安全日志' => $aqrz,
                '巡检抽查' => $xmxjcc,
                '进场验收' => $jcys,
                '专项检查' => $zxjc,
                '旁站记录' => $pzjl,
                '工程验收' => $gcys,
                '安全检查' => $aqjc,
                '项目发文' => $xmfw,
                '会议纪要' => $hyjl
            ];
            
            // 计算工作状态数据
            $workStatusData = [
                '已完成' => 7,
                '进行中' => 1,
                '待处理' => 1
            ];
            ?>
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">今日工作总数</h5>
                            <h2 class="card-text"><?php echo $sl; ?></h2>
                            <p class="text-success">+3 较昨日</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">已完成工作</h5>
                            <h2 class="card-text"><?php echo $sl; ?></h2>
                            <p class="text-success">75% 完成率</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">进行中工作</h5>
                            <h2 class="card-text">0</h2>
                            <p class="text-primary">21% 进行中</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">待处理工作</h5>
                            <h2 class="card-text">0</h2>
                            <p class="text-warning">4% 待处理</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">工作类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="workTypeDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">工作状态分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="workStatusDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他部分保持不变... -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">今日工作台账</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>工作类型</th>
                                            <th>项目名称</th>
                                            <th>工作内容</th>
                                            <th>负责人</th>
                                            <th>开始时间</th>
                                            <th>结束时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql="SELECT * FROM `tuqoa_xmxjcc` WHERE `xjrq`='$startDate' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                //$sl+=$row["sl"];
                                                //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="inspection">
                                            <td>
                                                <i class="bx bx-walk work-type-icon work-type-inspection"></i>
                                                巡视
                                            </td>
                                            <td><a href="/task.php?a=p&num=xmxjcc&mid=<?php echo $row["id"]; ?>" target="_blank"><?php echo $row["project"]; ?></a></td>
                                            <td><?php echo $row["dwgc"]; ?></td>
                                            <td><?php echo $row["xjr"]; ?></td>
                                            <td><?php echo $row["xjrq"]; ?></td>
                                            <td></td>
                                        </tr>
                                        <?php
                                            }
                                        }
                                        ?>

                                        <?php
                                        $sql="SELECT * FROM `tuqoa_jcys` WHERE `jcsj`='$startDate' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                //$sl+=$row["sl"];
                                                //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="acceptance">
                                            <td>
                                                <i class="bx bx-check-square work-type-icon work-type-acceptance"></i>
                                                进场验收
                                            </td>
                                            <td><a href="/task.php?a=p&num=jcys&mid=<?php echo $row["id"]; ?>" target="_blank"><?php echo $row["project"]; ?></a></td>
                                            <td><?php echo $row["dwgc"]; ?></td>
                                            <td><?php echo $row["jlysr"]; ?></td>
                                            <td><?php echo $row["jcsj"]; ?></td>
                                            <td></td>
                                        </tr>
                                        <?php
                                            }
                                        }
                                        ?>

                                        <?php
                                        $sql="SELECT * FROM `tuqoa_jzjy` WHERE `qysj`='$startDate' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            //$sl+=$row["sl"];
                                            //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="sampling">
                                            <td>
                                                <i class="bx bx-test-tube work-type-icon work-type-sampling"></i>
                                                见证检验
                                            </td>
                                            <td><a href="/task.php?a=p&num=jzjy&mid=<?=$row["id"]?>" target="_blank"><?=$row["project"]?></a></td>
                                            <td><?=$row["sjmc"]?></td>
                                            <td><?=$row["jzr"]?></td>
                                            <td><?=$row["qysj"]?></td>
                                            <td><?=$row["sjsj"]?></td>
                                        </tr>
                                        <?php } ?>

                                        <?php
                                        $sql="SELECT * FROM `tuqoa_pzjl` WHERE `kssj` like '$startDate%' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            //$sl+=$row["sl"];
                                            //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="supervision">
                                            <td>
                                                <i class="bx bx-show-alt work-type-icon work-type-supervision"></i>
                                                旁站
                                            </td>
                                            <td><a href="/task.php?a=p&num=pzjl&mid=<?=$row["id"]?>" target="_blank"><?=$row["project"]?></a></td>
                                            <td><?=$row["bwgx"]?></td>
                                            <td><?=$row["jlry"]?></td>
                                            <td><?= substr($row["kssj"], -9) ?></td>
                                            <td><?= substr($row["jssj"], -9) ?></td>
                                        </tr>
                                        <?php } ?>

                                        <?php
                                        $sql="SELECT * FROM `tuqoa_gcys` WHERE `yssj` like '$startDate%' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            //$sl+=$row["sl"];
                                            //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="verification">
                                            <td>
                                                <i class="bx bx-check-double work-type-icon work-type-verification"></i>
                                                工程验收
                                            </td>
                                            <td><a href="/task.php?a=p&num=gcys&mid=<?=$row["id"]?>" target="_blank"><?=$row["project"]?></a></td>
                                            <td><?=$row["sybw"]?></td>
                                            <td><?=$row["sybw"]?></td>
                                            <td><?= substr($row["yssj"], -9) ?></td>
                                            <td></td>
                                        </tr>
                                        <?php } ?>

                                        <?php
                                        $sql="SELECT * FROM `tuqoa_pxjc` WHERE `qysj` like '$startDate%' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            //$sl+=$row["sl"];
                                            //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="testing">
                                            <td>
                                                <i class="bx bx-line-chart work-type-icon work-type-testing"></i>
                                                平行检验
                                            </td>
                                            <td><a href="/task.php?a=p&num=pxjc&mid=<?=$row["id"]?>" target="_blank"><?=$row["project"]?></a></td>
                                            <td><?=$row["sjmc"]?></td>
                                            <td><?=$row["jyr"]?></td>
                                            <td><?= substr($row["qysj"], -9) ?></td>
                                            <td><?= substr($row["sjsj"], -9) ?></td>
                                        </tr>
                                        <?php } ?>

                                        <?php
                                        $sql="SELECT * FROM `tuqoa_hyjy` WHERE `kssj` = '$startDate' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            //$sl+=$row["sl"];
                                            //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="meeting">
                                            <td>
                                                <i class="bx bx-group work-type-icon work-type-meeting"></i>
                                                会议
                                            </td>
                                            <td><a href="/task.php?a=p&num=hyjy&mid=<?=$row["id"]?>" target="_blank"><?=$row["project"]?></a></td>
                                            <td><?=$row["hyzt"]?></td>
                                            <td><?=$row["jlr"]?></td>
                                            <td><?=$row["kssj"]?></td>
                                            <td>17:30</td>
                                        </tr>
                                        <?php } ?>

                                        <?php
                                        $sql="SELECT * FROM `tuqoa_jzjy` WHERE `qysj` like '$startDate%' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            //$sl+=$row["sl"];
                                            //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="inspection">
                                            <td>
                                                <i class="bx bx-walk work-type-icon work-type-inspection"></i>
                                                见证检验
                                            </td>
                                            <td><a href="/task.php?a=p&num=jzjy&mid=<?=$row["id"]?>" target="_blank"><?=$row["project"]?></a></td>
                                            <td><?=$row["sjmc"]?></td>
                                            <td><?=$row["jzr"]?></td>
                                            <td><?=$row["qysj"]?></td>
                                            <td><?=$row["sjsj"]?></td>
                                        </tr>
                                        <?php } ?>

                                        <?php
                                        $sql="SELECT * FROM `tuqoa_jlrz` WHERE `kssj` = '$startDate' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            //$sl+=$row["sl"];
                                            //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="acceptance">
                                            <td>
                                                <i class="bx bx-check-square work-type-icon work-type-acceptance"></i>
                                                监理日志
                                            </td>
                                             <td><a href="/task.php?a=p&num=jlrz&mid=<?=$row["id"]?>" target="_blank"><?=$row["project"]?></a></td>
                                            <td><?=$row["dwgc"]?></td>
                                            <td><?=$row["sname"]?></td>
                                            <td><?=$row["kssj"]?></td>
                                            <td></td>
                                        </tr>
                                        <?php } ?>

                                        <?php
                                        $sql="SELECT * FROM `tuqoa_aqrz` WHERE `kssj` = '$startDate' order by id desc";//order by id desc LIMIT 1
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            //$sl+=$row["sl"];
                                            //$xmxjcc=$row["sl"];
                                        ?>
                                        <tr data-type="acceptance">
                                            <td>
                                                <i class="bx bx-check-square work-type-icon work-type-acceptance"></i>
                                                安全日志
                                            </td>
                                            <td><a href="/task.php?a=p&num=aqrz&mid=<?=$row["id"]?>" target="_blank"><?=$row["project"]?></a></td>
                                            <td><?=$row["sgdw"]?></td>
                                            <td><?=$row["aqy"]?></td>
                                            <td><?=$row["kssj"]?></td>
                                            <td></td>
                                        </tr>
                                        <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            // 添加以下代码在$workStatusData定义之后
            $workTimeData = [
                '08:00-10:00' => 3,
                '10:00-12:00' => 5,
                '12:00-14:00' => 1,
                '14:00-16:00' => 4,
                '16:00-18:00' => 2,
                '18:00-20:00' => 1
            ];
            
            $projectWorkData = [
                '市政道路工程' => 12,
                '桥梁工程' => 5,
                '隧道工程' => 3,
                '排水工程' => 2
            ];
            ?>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">今日工作时间分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="workTimeDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目工作分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="projectWorkDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">工作统计汇总</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>工作类型</th>
                                            <th>今日数量</th>
                                            <th>已完成</th>
                                            <th>进行中</th>
                                            <th>待处理</th>
                                            <th>完成率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>监理日志</td>
                                            <td><?=$jlrz?></td>
                                            <td>1</td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>50%</td>
                                        </tr>
                                        <tr>
                                            <td>安全日志</td>
                                            <td><?=$aqrz?></td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>1</td>
                                            <td>50%</td>
                                        </tr>
                                        <tr>
                                            <td>巡检抽查</td>
                                            <td><?=$xmxjcc?></td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>0</td>
                                            <td>100%</td>
                                        </tr>
                                        <tr>
                                            <td>进场验收</td>
                                            <td><?=$jcys?></td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>0</td>
                                            <td>100%</td>
                                        </tr>
                                        <tr>
                                            <td>专项检查</td>
                                            <td><?=$zxjc?></td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>0</td>
                                            <td>100%</td>
                                        </tr>
                                        <tr>
                                            <td>旁站记录</td>
                                            <td><?=$pzjl?></td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>0</td>
                                            <td>100%</td>
                                        </tr>
                                        <tr>
                                            <td>工程验收</td>
                                            <td><?=$gcys?></td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>0</td>
                                            <td>100%</td>
                                        </tr>
                                        <tr>
                                            <td>安全检查</td>
                                            <td><?=$aqjc?></td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>0</td>
                                            <td>100%</td>
                                        </tr>
                                        <tr>
                                            <td>项目发文</td>
                                            <td><?=$xmfw?></td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>0</td>
                                            <td>100%</td>
                                        </tr>
                                        <tr>
                                            <td>会议纪要</td>
                                            <td><?=$hyjl?></td>
                                            <td>1</td>
                                            <td>0</td>
                                            <td>0</td>
                                            <td>100%</td>
                                        </tr>
                                        <tr>
                                            <td>合计</td>
                                            <td><?=$sl?></td>
                                            <td>7</td>
                                            <td>1</td>
                                            <td>1</td>
                                            <td>77.8%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="modal fade" id="addWorkModal" tabindex="-1" aria-labelledby="addWorkModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
           
        </div>
    </div>
        
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认日期为今天
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                // 这里可以添加查询逻辑
                console.log('查询按钮被点击');
            });
        });
        
        // 初始化图表
        function initCharts() {
            // 工作类型分布图表
            const workTypeDistributionCtx = document.getElementById('workTypeDistributionChart').getContext('2d');
            const workTypeData = <?php echo json_encode(array_values($workTypeData)); ?>;
            const workTypeLabels = <?php echo json_encode(array_keys($workTypeData)); ?>;
            
            new Chart(workTypeDistributionCtx, {
                type: 'pie',
                data: {
                    labels: workTypeLabels,
                    datasets: [{
                        data: workTypeData,
                        backgroundColor: [
                            '#1e88e5', '#43a047', '#e53935', '#fb8c00', 
                            '#8e24aa', '#00acc1', '#6d4c41', '#757575'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: true,
                            text: '工作类型分布'
                        }
                    }
                }
            });
            
            // 工作状态分布图表
            const workStatusDistributionCtx = document.getElementById('workStatusDistributionChart').getContext('2d');
            const workStatusData = <?php echo json_encode(array_values($workStatusData)); ?>;
            const workStatusLabels = <?php echo json_encode(array_keys($workStatusData)); ?>;
            
            new Chart(workStatusDistributionCtx, {
                type: 'pie',
                data: {
                    labels: workStatusLabels,
                    datasets: [{
                        data: workStatusData,
                        backgroundColor: ['#4caf50', '#2196f3', '#ffeb3b'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: true,
                            text: '工作状态分布'
                        }
                    }
                }
            });
            
            // 今日工作时间分布图表
            const workTimeDistributionCtx = document.getElementById('workTimeDistributionChart').getContext('2d');
            new Chart(workTimeDistributionCtx, {
                type: 'bar',
                data: {
                    labels: ['08:00-10:00', '10:00-12:00', '12:00-14:00', '14:00-16:00', '16:00-18:00', '18:00-20:00'],
                    datasets: [{
                        label: '工作数量',
                        data: [3, 5, 1, 4, 2, 1],
                        backgroundColor: '#1e88e5',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '今日工作时间分布'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // 项目工作分布图表
            const projectWorkDistributionCtx = document.getElementById('projectWorkDistributionChart').getContext('2d');
            new Chart(projectWorkDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['房建工程', '市政工程', '咨询类', '管理类'],
                    datasets: [{
                        data: [12, 5, 3, 2],
                        backgroundColor: [
                            '#1e88e5',
                            '#43a047',
                            '#e53935',
                            '#fb8c00'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '项目工作分布'
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>