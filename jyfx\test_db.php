<?php
// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>数据库连接测试</h2>";

include '../config.php';

// 测试数据库连接
if ($link) {
    echo "<p style='color: green;'>✓ 数据库连接成功</p>";
    
    // 测试各个表是否存在
    $tables = ['tuqoa_htgl', 'tuqoa_xmcztjb', 'tuqoa_rydp', 'tuqoa_htsf', 'tuqoa_hrsalary', 'tuqoa_xmhstjzl'];
    
    foreach ($tables as $table) {
        $sql = "SHOW TABLES LIKE '$table'";
        $result = mysqli_query($link, $sql);
        if ($result && mysqli_num_rows($result) > 0) {
            echo "<p style='color: green;'>✓ 表 $table 存在</p>";
            
            // 检查表中是否有数据
            $sql = "SELECT COUNT(*) as count FROM `$table`";
            $result = mysqli_query($link, $sql);
            if ($result) {
                $row = mysqli_fetch_assoc($result);
                echo "<p style='color: blue;'>  - 表 $table 有 {$row['count']} 条记录</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ 表 $table 不存在</p>";
        }
    }
    
    // 测试基本查询
    echo "<h3>测试基本查询</h3>";
    
    $sql = "SELECT COUNT(*) as count FROM `tuqoa_htgl`";
    $result = mysqli_query($link, $sql);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "<p>合同表记录数: {$row['count']}</p>";
    } else {
        echo "<p style='color: red;'>查询合同表失败: " . mysqli_error($link) . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ 数据库连接失败: " . mysqli_connect_error() . "</p>";
}
?>
