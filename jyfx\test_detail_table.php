<?php
// 启用错误报告以便调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

include '../config.php';

// 检查数据库连接
if (!isset($link) || !$link) {
    die("数据库连接失败");
}

$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>月度经营数据明细 - 测试版</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h2>月度经营数据明细 - 测试版</h2>
    
    <form method="post" action="">
        <label for="start-date">开始日期:</label>
        <input type="date" id="start-date" name="start-date" value="<?php echo htmlspecialchars($startDate); ?>">
        <label for="end-date">结束日期:</label>
        <input type="date" id="end-date" name="end-date" value="<?php echo htmlspecialchars($endDate); ?>">
        <button type="submit">查询</button>
    </form>
    
    <p>查询日期范围: <?=$startDate?> 到 <?=$endDate?></p>
    
    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th>月份</th>
                    <th>项目总投资</th>
                    <th>服务费合计</th>
                    <th>预计收款</th>
                    <th>到账</th>
                    <th>差额</th>
                    <th>月人数</th>
                    <th>工资汇总</th>
                    <th>工资占比</th>
                    <th>成本汇总</th>
                    <th>成本占比</th>
                    <th>人均产值</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // 使用简化的查询，按月份分组显示数据
                $sql="SELECT DATE_FORMAT(qdsj, '%Y-%m') AS month,
                             COALESCE(SUM(ztz), 0) AS ztzhj,
                             COALESCE(SUM(fwf), 0) AS fwfhj
                      FROM `tuqoa_htgl`
                      WHERE `qdsj` >= '$startDate' AND `qdsj` <= '$endDate'
                      GROUP BY DATE_FORMAT(qdsj, '%Y-%m')
                      ORDER BY month";

                echo "<p><strong>SQL查询:</strong> $sql</p>";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    $rowCount = 0;
                    while ($row = mysqli_fetch_assoc($result)) {
                        $rowCount++;
                        // 获取基本数据
                        $合同签订月份 = $row["month"];
                        $总投资合计 = $row["ztzhj"];
                        $服务费合计 = $row["fwfhj"];
                        
                        echo "<p class='success'>处理第 $rowCount 行数据: $合同签订月份</p>";
                        
                        // 初始化所有变量
                        $应回收款 = 0;
                        $已收回款 = 0;
                        $差额 = 0;
                        $月人数 = 0;
                        $应发工资合计 = 0;
                        $预算成本费用 = 0;
                        $预算直接费 = 0;
                        $企业管理费 = 0;
                        $经营业务费 = 0;
                        $员工社保等上缴金额合计 = 0;
                        $实际成本费用 = 0;
                        $实际直接费 = 0;
                        $完成产值 = 0;
                        $人均产值 = 0;
                        
                        // 查询收款数据
                        $sql1 = "SELECT ifnull(sum(yjje),0) as yjjehj,ifnull(sum(ysje),0) as ysjehj FROM `tuqoa_htsf` WHERE `yjsj` like '".$合同签订月份."%'";
                        $result1 = mysqli_query($link, $sql1);
                        if ($result1) {
                            $row1 = mysqli_fetch_assoc($result1);
                            $应回收款 = $row1["yjjehj"];
                            $已收回款 = $row1["ysjehj"];
                            $差额 = $应回收款 - $已收回款;
                            echo "<p>收款查询成功: 应收 $应回收款, 已收 $已收回款</p>";
                        } else {
                            echo "<p class='error'>收款查询失败: " . mysqli_error($link) . "</p>";
                        }
                        
                        // 查询工资数据
                        $sql1 = "SELECT count(*) yrs,sum(sfgz) sfgzhj FROM `tuqoa_hrsalary` WHERE `month`='".$合同签订月份."'";
                        $result1 = mysqli_query($link, $sql1);
                        if ($result1) {
                            $row1 = mysqli_fetch_assoc($result1);
                            $月人数 = $row1["yrs"];
                            $应发工资合计 = $row1["sfgzhj"];
                            echo "<p>工资查询成功: 人数 $月人数, 工资 $应发工资合计</p>";
                        } else {
                            echo "<p class='error'>工资查询失败: " . mysqli_error($link) . "</p>";
                        }
                        
                        // 查询成本数据
                        $sql1 = "SELECT * FROM `tuqoa_xmhstjzl` WHERE `sbrq` like '".$合同签订月份."%'";
                        $result1 = mysqli_query($link, $sql1);
                        if ($result1) {
                            $row1 = mysqli_fetch_assoc($result1);
                            if ($row1) {
                                $预算成本费用 = $row1["yszcbfy"];
                                $预算直接费 = $row1["yszjf"];
                                $企业管理费 = $row1["qyglf"];
                                $经营业务费 = $row1["jyywf"];
                                echo "<p>成本查询成功: 企业管理费 $企业管理费, 经营业务费 $经营业务费</p>";
                            } else {
                                echo "<p>成本查询无数据</p>";
                            }
                        } else {
                            echo "<p class='error'>成本查询失败: " . mysqli_error($link) . "</p>";
                        }
                        
                        // 计算实际成本
                        $实际成本费用 = $应发工资合计 + $员工社保等上缴金额合计 + $企业管理费 + $经营业务费;
                        $实际直接费 = $应发工资合计 + $员工社保等上缴金额合计;
                        
                        // 查询产值数据
                        $sql1 = "SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb` WHERE `sbrq` like '".$合同签订月份."%'";
                        $result1 = mysqli_query($link, $sql1);
                        if ($result1) {
                            $row1 = mysqli_fetch_assoc($result1);
                            $完成产值 = $row1["hj"];
                            echo "<p>产值查询成功: $完成产值</p>";
                        } else {
                            echo "<p class='error'>产值查询失败: " . mysqli_error($link) . "</p>";
                        }
                        
                        // 计算人均产值
                        if ($月人数 > 0) {
                            $人均产值 = number_format($完成产值 / $月人数, 4);
                        }
                        
                        echo "<hr>";
                ?>
                <tr>
                    <td><?=$合同签订月份?></td>
                    <td><?=$总投资合计?>万</td>
                    <td><?=$服务费合计?>万</td>
                    <td>¥<?=$应回收款?>万</td>
                    <td>¥<?=$已收回款?>万</td>
                    <td>¥<?=$差额?>万</td>
                    <td><?=$月人数?>人</td>
                    <td><?=$应发工资合计?>元</td>
                    <td>15%</td>
                    <td>¥<?=$实际成本费用?></td>
                    <td>65%</td>
                    <td><?=$人均产值?>万</td>
                </tr>
                <?php
                    }
                    if ($rowCount == 0) {
                        echo "<tr><td colspan='12'>在指定日期范围内没有找到数据</td></tr>";
                    } else {
                        echo "<p class='success'>总共处理了 $rowCount 行数据</p>";
                    }
                } else {
                    echo "<tr><td colspan='12' class='error'>数据库查询错误: " . mysqli_error($link) . "</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>
    
    <hr>
    <p><strong>如果这个测试页面正常显示数据，说明"月度经营数据明细"部分的逻辑是正确的。</strong></p>
    <p><a href="ydjysjfx.php">返回完整页面</a></p>
</body>
</html>
