<?php
include '../config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目合同汇总分析 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
            transition: all 0.3s ease;
        }

        .chart-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .card {
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .no-data-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(248, 249, 250, 0.95);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
            z-index: 10;
        }
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">项目合同汇总分析</h2>
            
            <!-- 时间范围选择器 -->
            <form method="post" action="">
            <div class="filter-row mb-4">
                <div class="filter-item">
                    <label for="start-date">开始日期：</label>
                    <input type="date" id="start-date" class="form-control" style="width: auto; display: inline-block;" name="start_date"
                           value="<?php echo isset($_POST['start_date']) ? $_POST['start_date'] : '2020-01-01'; ?>">
                </div>
                <div class="filter-item">
                    <label for="end-date">结束日期：</label>
                    <input type="date" id="end-date" class="form-control" style="width: auto; display: inline-block;" name="end_date"
                           value="<?php echo isset($_POST['end_date']) ? $_POST['end_date'] : date('Y-m-d'); ?>">
                </div>
                <button type="submit" id="query-btn" class="btn btn-primary">查询</button>
            </div>
            </form>
            
            <div class="row">
                <?php
                // 获取时间范围 - 扩大默认范围以确保有数据
                $start_date = isset($_POST['start_date']) ? $_POST['start_date'] : '2020-01-01';
                $end_date = isset($_POST['end_date']) ? $_POST['end_date'] : date('Y-m-d');

                // 调试：检查数据库中是否有合同数据
                $debug_sql = "SELECT COUNT(*) as total_contracts FROM `tuqoa_htgl`";
                $debug_result = mysqli_query($link, $debug_sql);
                $total_contracts = 0;
                if ($debug_result) {
                    $debug_row = mysqli_fetch_assoc($debug_result);
                    $total_contracts = $debug_row['total_contracts'];
                }

                // 如果没有合同数据，调整查询策略
                if ($total_contracts == 0) {
                    // 移除时间限制，查询所有数据
                    $time_filter = "1=1";
                    $time_filter_h = "1=1";  // 带表前缀h的版本
                } else {
                    $time_filter = "qdsj >= '$start_date' AND qdsj <= '$end_date'";
                    $time_filter_h = "h.qdsj >= '$start_date' AND h.qdsj <= '$end_date'";
                }

                // 计算合同总额（全部项目）
                $contract_total=0;
                $sql="SELECT IFNULL(SUM(fwf),0) as hj FROM `tuqoa_htgl` WHERE $time_filter";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $contract_total=$row["hj"];
                    }
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">合同总额</h5>
                            <h2 class="card-text"><?php echo $contract_total?>万</h2>
                        </div>
                    </div>
                </div>
                <?php
                // 计算已收金额（全部项目）
                $received_amount=0;
                // 对于收费表，如果没有合同数据就查询所有收费数据
                if ($total_contracts == 0) {
                    $sql="SELECT IFNULL(SUM(ysje),0) as hj FROM `tuqoa_htsf`";
                } else {
                    $sql="SELECT IFNULL(SUM(ysje),0) as hj FROM `tuqoa_htsf` WHERE `sksj` >= '$start_date' AND `sksj` <= '$end_date'";
                }
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $received_amount=$row["hj"];
                    }
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">已收款总额</h5>
                            <h2 class="card-text">¥<?php echo $received_amount?>万</h2>
                        </div>
                    </div>
                </div>
                <?php
                // 计算平均完成率（全部项目）
                $completion_rate=0;
                $sql="SELECT IFNULL(AVG(wcl),0) as hj FROM `tuqoa_xmcztjb` WHERE `sbrq` >= '$start_date' AND `sbrq` <= '$end_date'";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $completion_rate=round($row["hj"], 1);
                    }
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">平均完成率</h5>
                            <h2 class="card-text"><?php echo $completion_rate?>%</h2>
                        </div>
                    </div>
                </div>
                <?php
                // 计算总产值（全部项目）
                $project_total_value=0;
                $sql="SELECT IFNULL(SUM(wccz),0) as hj FROM `tuqoa_xmcztjb` WHERE `sbrq` >= '$start_date' AND `sbrq` <= '$end_date'";
                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $project_total_value=$row["hj"];
                    }
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">项目总产值</h5>
                            <h2 class="card-text">¥<?php echo $project_total_value?>万</h2>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款与计划对比</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyPaymentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
                // 初始化图表数据变量
                $收款日期 = [];
                $预计收款金额 = [];
                $已收金额 = [];
                $月份 = [];
                $应发工资合计 = [];
                $每月人数 = [];

                // 查询收款数据
                $sql_payment = "SELECT
                    DATE_FORMAT(yjsj, '%Y-%m') as month,
                    SUM(yjje) as planned_amount,
                    SUM(ysje) as received_amount
                FROM tuqoa_htsf
                WHERE yjsj >= '$start_date' AND yjsj <= '$end_date'
                GROUP BY DATE_FORMAT(yjsj, '%Y-%m')
                ORDER BY month DESC
                LIMIT 6";

                $result_payment = mysqli_query($link, $sql_payment);
                if ($result_payment) {
                    while ($row_payment = mysqli_fetch_assoc($result_payment)) {
                        $收款日期[] = $row_payment['month'];
                        $预计收款金额[] = floatval($row_payment['planned_amount']);
                        $已收金额[] = floatval($row_payment['received_amount']);
                    }
                }

                // 如果没有数据，提供默认数据
                if (empty($收款日期)) {
                    $收款日期 = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'];
                    $预计收款金额 = [0, 0, 0, 0, 0, 0];
                    $已收金额 = [0, 0, 0, 0, 0, 0];
                }

                // 初始化默认数据（移除工资查询，因为与合同汇总分析主题不符）
                $月份 = [];
                $应发工资合计 = [];
                $每月人数 = [];

                // 提供默认数据
                if (empty($月份)) {
                    $月份 = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'];
                    $应发工资合计 = [0, 0, 0, 0, 0, 0];
                    $每月人数 = [0, 0, 0, 0, 0, 0];
                }

                // 反转数组以按时间顺序显示
                $收款日期 = array_reverse($收款日期);
                $预计收款金额 = array_reverse($预计收款金额);
                $已收金额 = array_reverse($已收金额);
                $月份 = array_reverse($月份);
                $应发工资合计 = array_reverse($应发工资合计);
                $每月人数 = array_reverse($每月人数);

                // 查询合同汇总分析数据

                // 1. 合同签订趋势数据（按月统计）
                $contract_months = [];
                $contract_counts = [];
                $contract_amounts = [];

                $sql = "SELECT
                    DATE_FORMAT(qdsj, '%Y-%m') as month,
                    COUNT(*) as contract_count,
                    IFNULL(SUM(fwf), 0) as total_amount
                FROM `tuqoa_htgl`
                WHERE $time_filter
                GROUP BY DATE_FORMAT(qdsj, '%Y-%m')
                ORDER BY month ASC";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $contract_months[] = substr($row["month"], -2) . "月";
                        $contract_counts[] = (int)$row["contract_count"];
                        $contract_amounts[] = (float)$row["total_amount"];
                    }
                }

                if (empty($contract_months)) {
                    // 生成最近6个月的标签，显示趋势结构
                    $contract_months = [];
                    $contract_counts = [];
                    $contract_amounts = [];
                    for ($i = 5; $i >= 0; $i--) {
                        $month = date('m月', strtotime("-$i months"));
                        $contract_months[] = $month;
                        $contract_counts[] = 0;
                        $contract_amounts[] = 0;
                    }
                }

                // 2. 合同类型分布数据
                $contract_types = [];
                $contract_type_counts = [];

                $sql = "SELECT
                    CASE
                        WHEN htmc LIKE '%设计%' THEN '设计合同'
                        WHEN htmc LIKE '%施工%' THEN '施工合同'
                        WHEN htmc LIKE '%监理%' THEN '监理合同'
                        WHEN htmc LIKE '%咨询%' THEN '咨询合同'
                        ELSE '其他合同'
                    END as contract_type,
                    COUNT(*) as type_count,
                    IFNULL(SUM(fwf), 0) as type_amount
                FROM `tuqoa_htgl`
                WHERE $time_filter
                GROUP BY contract_type";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $contract_types[] = $row['contract_type'];
                        $contract_type_counts[] = (int)$row['type_count'];
                    }
                }

                if (empty($contract_types)) {
                    // 显示常见的合同类型结构，即使没有数据也能展示业务框架
                    $contract_types = ['设计合同', '施工合同', '监理合同', '咨询合同', '其他合同'];
                    $contract_type_counts = [0, 0, 0, 0, 0];
                }

                // 3. 合同执行状态数据
                $contract_status = [];
                $contract_status_counts = [];

                $sql = "SELECT
                    CASE
                        WHEN s.ysje >= h.fwf THEN '已完成'
                        WHEN s.ysje > 0 THEN '执行中'
                        ELSE '未开始'
                    END as status,
                    COUNT(*) as status_count
                FROM `tuqoa_htgl` h
                LEFT JOIN `tuqoa_htsf` s ON h.id = s.htid
                WHERE $time_filter_h
                GROUP BY status";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $contract_status[] = $row['status'];
                        $contract_status_counts[] = (int)$row['status_count'];
                    }
                }

                if (empty($contract_status)) {
                    // 显示合同执行的标准状态分类
                    $contract_status = ['未开始', '执行中', '已完成'];
                    $contract_status_counts = [0, 0, 0];
                }

                // 4. 合同金额分布数据
                $amount_ranges = [];
                $amount_counts = [];

                $sql = "SELECT
                    CASE
                        WHEN fwf <= 50 THEN '50万以下'
                        WHEN fwf <= 100 THEN '50-100万'
                        WHEN fwf <= 200 THEN '100-200万'
                        WHEN fwf <= 500 THEN '200-500万'
                        ELSE '500万以上'
                    END as amount_range,
                    COUNT(*) as range_count
                FROM `tuqoa_htgl`
                WHERE $time_filter
                GROUP BY amount_range
                ORDER BY
                    CASE
                        WHEN fwf <= 50 THEN 1
                        WHEN fwf <= 100 THEN 2
                        WHEN fwf <= 200 THEN 3
                        WHEN fwf <= 500 THEN 4
                        ELSE 5
                    END";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $amount_ranges[] = $row['amount_range'];
                        $amount_counts[] = (int)$row['range_count'];
                    }
                }

                if (empty($amount_ranges)) {
                    // 显示标准的金额分档，帮助用户理解分类标准
                    $amount_ranges = ['50万以下', '50-100万', '100-200万', '200-500万', '500万以上'];
                    $amount_counts = [0, 0, 0, 0, 0];
                }

                // 5. 收款进度分析数据
                $payment_progress = [];
                $payment_progress_data = [];

                $sql = "SELECT
                    h.htmc as contract_name,
                    h.fwf as contract_amount,
                    IFNULL(SUM(s.ysje), 0) as received_amount,
                    CASE
                        WHEN h.fwf > 0 THEN ROUND((IFNULL(SUM(s.ysje), 0) / h.fwf) * 100, 1)
                        ELSE 0
                    END as progress_rate
                FROM `tuqoa_htgl` h
                LEFT JOIN `tuqoa_htsf` s ON h.id = s.htid
                WHERE h.qdsj >= '$start_date' AND h.qdsj <= '$end_date'
                GROUP BY h.id, h.htmc, h.fwf
                ORDER BY h.fwf DESC
                LIMIT 10";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $payment_progress[] = substr($row['contract_name'], 0, 10) . '...';
                        $payment_progress_data[] = (float)$row['progress_rate'];
                    }
                }

                if (empty($payment_progress)) {
                    // 显示示例合同进度结构
                    $payment_progress = ['等待合同数据...'];
                    $payment_progress_data = [0];
                }

                // 6. 合同收款时效分析
                $timing_analysis = [];
                $timing_counts = [];

                $sql = "SELECT
                    CASE
                        WHEN s.sksj IS NULL OR s.sksj = '0000-00-00' THEN '未收款'
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 30 THEN '30天内'
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 90 THEN '31-90天'
                        WHEN DATEDIFF(s.sksj, h.qdsj) <= 180 THEN '91-180天'
                        ELSE '超过180天'
                    END as timing_range,
                    COUNT(*) as timing_count
                FROM `tuqoa_htgl` h
                LEFT JOIN `tuqoa_htsf` s ON h.id = s.htid
                WHERE h.qdsj >= '$start_date' AND h.qdsj <= '$end_date'
                GROUP BY timing_range";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $timing_analysis[] = $row['timing_range'];
                        $timing_counts[] = (int)$row['timing_count'];
                    }
                }

                if (empty($timing_analysis)) {
                    // 显示收款时效的标准分类
                    $timing_analysis = ['30天内', '31-90天', '91-180天', '超过180天', '未收款'];
                    $timing_counts = [0, 0, 0, 0, 0];
                }

                // 8. 合同产值对比分析数据
                $contract_value_comparison = [];
                $contract_amounts_data = [];
                $completed_values_data = [];

                $sql = "SELECT
                    h.htmc as contract_name,
                    h.fwf as contract_amount,
                    IFNULL(SUM(c.wccz), 0) as completed_value,
                    CASE
                        WHEN h.fwf > 0 THEN ROUND((IFNULL(SUM(c.wccz), 0) / h.fwf) * 100, 1)
                        ELSE 0
                    END as completion_rate
                FROM `tuqoa_htgl` h
                LEFT JOIN `tuqoa_gcproject` p ON h.projectid = p.id
                LEFT JOIN `tuqoa_xmcztjb` c ON p.id = c.projectid
                WHERE h.qdsj >= '$start_date' AND h.qdsj <= '$end_date'
                GROUP BY h.id, h.htmc, h.fwf
                ORDER BY h.fwf DESC
                LIMIT 8";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $contract_value_comparison[] = substr($row['contract_name'], 0, 12) . '...';
                        $contract_amounts_data[] = (float)$row['contract_amount'];
                        $completed_values_data[] = (float)$row['completed_value'];
                    }
                }

                if (empty($contract_value_comparison)) {
                    // 显示等待数据的提示
                    $contract_value_comparison = ['等待合同数据...'];
                    $contract_amounts_data = [0];
                    $completed_values_data = [0];
                }

                // 9. 月度合同签订与产值完成趋势
                $monthly_trend_labels = [];
                $monthly_contracts_data = [];
                $monthly_values_data = [];

                $sql = "SELECT
                    DATE_FORMAT(h.qdsj, '%Y-%m') as month,
                    COUNT(h.id) as contract_count,
                    IFNULL(SUM(h.fwf), 0) as contract_total,
                    IFNULL(SUM(c.wccz), 0) as value_total
                FROM `tuqoa_htgl` h
                LEFT JOIN `tuqoa_gcproject` p ON h.projectid = p.id
                LEFT JOIN `tuqoa_xmcztjb` c ON p.id = c.projectid
                    AND DATE_FORMAT(h.qdsj, '%Y-%m') = DATE_FORMAT(c.sbrq, '%Y-%m')
                WHERE h.qdsj >= '$start_date' AND h.qdsj <= '$end_date'
                GROUP BY DATE_FORMAT(h.qdsj, '%Y-%m')
                ORDER BY month ASC";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $monthly_trend_labels[] = substr($row['month'], -2) . '月';
                        $monthly_contracts_data[] = (float)$row['contract_total'];
                        $monthly_values_data[] = (float)$row['value_total'];
                    }
                }

                if (empty($monthly_trend_labels)) {
                    // 生成最近6个月的趋势框架
                    $monthly_trend_labels = [];
                    $monthly_contracts_data = [];
                    $monthly_values_data = [];
                    for ($i = 5; $i >= 0; $i--) {
                        $month = date('m月', strtotime("-$i months"));
                        $monthly_trend_labels[] = $month;
                        $monthly_contracts_data[] = 0;
                        $monthly_values_data[] = 0;
                    }
                }



                // 11. 收款风险评估数据
                $risk_assessment = [];
                $risk_counts = [];

                $sql = "SELECT
                    CASE
                        WHEN s.ysje >= h.fwf THEN '无风险'
                        WHEN s.ysje >= h.fwf * 0.8 THEN '低风险'
                        WHEN s.ysje >= h.fwf * 0.5 THEN '中风险'
                        WHEN s.ysje > 0 THEN '高风险'
                        ELSE '极高风险'
                    END as risk_level,
                    COUNT(*) as risk_count
                FROM `tuqoa_htgl` h
                LEFT JOIN `tuqoa_htsf` s ON h.id = s.htid
                WHERE h.qdsj >= '$start_date' AND h.qdsj <= '$end_date'
                GROUP BY risk_level";

                $result = mysqli_query($link, $sql);
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $risk_assessment[] = $row['risk_level'];
                        $risk_counts[] = (int)$row['risk_count'];
                    }
                }

                if (empty($risk_assessment)) {
                    // 显示风险评估的标准分级
                    $risk_assessment = ['无风险', '低风险', '中风险', '高风险', '极高风险'];
                    $risk_counts = [0, 0, 0, 0, 0];
                }
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同签订趋势分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contractTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contractTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同执行状态</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contractStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目合同明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>合同名称</th>
                                            <th>合同编号</th>
                                            <th>签订日期</th>
                                            <th>合同金额(万)</th>
                                            <th>已收金额(万)</th>
                                            <th>收款进度</th>
                                            <th>合同状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql="SELECT
                                                h.htmc,
                                                h.htbh,
                                                h.qdsj,
                                                h.fwf,
                                                p.gcname,
                                                IFNULL(SUM(s.ysje), 0) as received_amount,
                                                CASE
                                                    WHEN h.fwf > 0 THEN ROUND((IFNULL(SUM(s.ysje), 0) / h.fwf) * 100, 1)
                                                    ELSE 0
                                                END as progress_rate,
                                                CASE
                                                    WHEN IFNULL(SUM(s.ysje), 0) >= h.fwf THEN '已完成'
                                                    WHEN IFNULL(SUM(s.ysje), 0) > 0 THEN '执行中'
                                                    ELSE '未开始'
                                                END as contract_status
                                            FROM `tuqoa_htgl` h
                                            LEFT JOIN `tuqoa_gcproject` p ON h.projectid = p.id
                                            LEFT JOIN `tuqoa_htsf` s ON h.id = s.htid
                                            WHERE h.qdsj >= '$start_date' AND h.qdsj <= '$end_date'
                                            GROUP BY h.id, h.htmc, h.htbh, h.qdsj, h.fwf, p.gcname
                                            ORDER BY h.qdsj DESC";

                                        $total_contract_amount = 0;
                                        $total_received_amount = 0;

                                        $result = mysqli_query($link, $sql);
                                        if ($result) {
                                            while ($row = mysqli_fetch_assoc($result)) {
                                                $total_contract_amount += $row["fwf"];
                                                $total_received_amount += $row["received_amount"];

                                                $status_class = '';
                                                if ($row["contract_status"] == '已完成') {
                                                    $status_class = 'text-success';
                                                } elseif ($row["contract_status"] == '执行中') {
                                                    $status_class = 'text-warning';
                                                } else {
                                                    $status_class = 'text-danger';
                                                }
                                        ?>
                                        <tr>
                                            <td><?php echo $row["gcname"] ? $row["gcname"] : '未关联项目'?></td>
                                            <td><?php echo $row["htmc"]?></td>
                                            <td><?php echo $row["htbh"]?></td>
                                            <td><?php echo $row["qdsj"]?></td>
                                            <td>¥<?php echo number_format($row["fwf"], 2)?></td>
                                            <td>¥<?php echo number_format($row["received_amount"], 2)?></td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar"
                                                         style="width: <?php echo $row["progress_rate"]?>%"
                                                         aria-valuenow="<?php echo $row["progress_rate"]?>"
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        <?php echo $row["progress_rate"]?>%
                                                    </div>
                                                </div>
                                            </td>
                                            <td><span class="<?php echo $status_class?>"><?php echo $row["contract_status"]?></span></td>
                                        </tr>
                                        <?php
                                            } // End while loop
                                        } // End if result

                                        $overall_progress = $total_contract_amount > 0 ? round(($total_received_amount / $total_contract_amount) * 100, 1) : 0;
                                        ?>
                                        <tr class="table-info">
                                            <td><strong>合计</strong></td>
                                            <td>--</td>
                                            <td>--</td>
                                            <td>--</td>
                                            <td><strong>¥<?php echo number_format($total_contract_amount, 2)?></strong></td>
                                            <td><strong>¥<?php echo number_format($total_received_amount, 2)?></strong></td>
                                            <td><strong><?php echo $overall_progress?>%</strong></td>
                                            <td>--</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同金额分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contractAmountChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款进度分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentProgressChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款时效分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentTimingChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款风险评估</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="riskAssessmentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同产值对比分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contractValueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度合同签订与产值完成趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            

        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>

        
        // 检查数据是否为空的辅助函数
        function isDataEmpty(data) {
            if (!data || data.length === 0) return true;
            return data.every(value => value === 0 || value === null || value === undefined);
        }

        // 简化的无数据检查函数
        function addNoDataMessage(chartInstance, message) {
            const canvas = chartInstance.canvas;
            const container = canvas.parentElement;

            // 检查是否有数据
            let hasData = false;
            for (let dataset of chartInstance.data.datasets) {
                if (dataset.data && dataset.data.some(value => value > 0)) {
                    hasData = true;
                    break;
                }
            }

            if (!hasData) {
                // 创建无数据提示覆盖层
                const overlay = document.createElement('div');
                overlay.className = 'no-data-overlay';
                overlay.innerHTML = `
                    <div style="font-size: 48px; margin-bottom: 15px; color: #dee2e6;">📊</div>
                    <div style="font-size: 16px; font-weight: bold; color: #6c757d; margin-bottom: 8px;">暂无数据</div>
                    <div style="font-size: 12px; color: #adb5bd;">${message || '请调整时间范围或等待数据录入'}</div>
                `;

                container.style.position = 'relative';
                container.appendChild(overlay);
            }
        }

        // 初始化图表
        function initCharts() {
            console.log('开始初始化图表...');

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js 未加载');
                return;
            }

            console.log('Chart.js 版本:', Chart.version);

            // 测试数据（如果PHP数据为空，使用测试数据）
            const testData = {
                收款日期: ['01月', '02月', '03月', '04月', '05月', '06月'],
                预计收款金额: [100, 150, 200, 180, 220, 250],
                已收金额: [80, 120, 180, 160, 200, 230],
                合同类型: ['设计合同', '施工合同', '监理合同', '咨询合同', '其他合同'],
                合同类型数量: [5, 8, 3, 2, 1]
            };

            // 1. 收款与计划对比图表
            const monthlyPaymentElement = document.getElementById('monthlyPaymentChart');
            if (!monthlyPaymentElement) {
                console.error('找不到 monthlyPaymentChart 元素');
                return;
            }

            const monthlyPaymentCtx = monthlyPaymentElement.getContext('2d');
            console.log('创建第一个图表...');

            // 获取PHP数据
            const phpData1 = {
                labels: <?php echo json_encode($收款日期); ?>,
                planned: <?php echo json_encode($预计收款金额); ?>,
                received: <?php echo json_encode($已收金额); ?>
            };

            // 检查数据是否有效，如果无效则使用测试数据
            const chart1Data = {
                labels: (phpData1.labels && phpData1.labels.length > 0) ? phpData1.labels : testData.收款日期,
                planned: (phpData1.planned && phpData1.planned.length > 0) ? phpData1.planned : testData.预计收款金额,
                received: (phpData1.received && phpData1.received.length > 0) ? phpData1.received : testData.已收金额
            };

            console.log('图表1数据:', chart1Data);

            const chart1 = new Chart(monthlyPaymentCtx, {
                type: 'bar',
                data: {
                    labels: chart1Data.labels,
                    datasets: [
                        {
                            label: '计划收款',
                            data: chart1Data.planned,
                            backgroundColor: '#1e88e5'
                        },
                        {
                            label: '实际收款',
                            data: chart1Data.received,
                            backgroundColor: '#43a047'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart1, '暂无收款数据，请检查合同收费记录');

            // 2. 合同签订趋势分析图表
            const contractTrendCtx = document.getElementById('contractTrendChart').getContext('2d');

            // 获取PHP数据
            const phpData2 = {
                labels: <?php echo json_encode($contract_months); ?>,
                counts: <?php echo json_encode($contract_counts); ?>,
                amounts: <?php echo json_encode($contract_amounts); ?>
            };

            // 检查数据是否有效
            const chart2Data = {
                labels: (phpData2.labels && phpData2.labels.length > 0) ? phpData2.labels : testData.收款日期,
                counts: (phpData2.counts && phpData2.counts.length > 0) ? phpData2.counts : [2, 3, 1, 4, 2, 3],
                amounts: (phpData2.amounts && phpData2.amounts.length > 0) ? phpData2.amounts : [200, 350, 150, 400, 250, 300]
            };

            console.log('图表2数据:', chart2Data);

            const chart2 = new Chart(contractTrendCtx, {
                type: 'line',
                data: {
                    labels: chart2Data.labels,
                    datasets: [
                        {
                            label: '合同数量',
                            data: chart2Data.counts,
                            borderColor: '#1e88e5',
                            backgroundColor: 'rgba(30, 136, 229, 0.1)',
                            yAxisID: 'y',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '合同金额(万)',
                            data: chart2Data.amounts,
                            borderColor: '#e53935',
                            backgroundColor: 'rgba(229, 57, 53, 0.1)',
                            yAxisID: 'y1',
                            tension: 0.3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '合同数量'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '金额（万元）'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart2, '暂无合同签订数据，请检查合同记录');

            // 3. 合同类型分布图表
            const contractTypeCtx = document.getElementById('contractTypeChart').getContext('2d');

            // 获取PHP数据
            const phpData3 = {
                labels: <?php echo json_encode($contract_types); ?>,
                counts: <?php echo json_encode($contract_type_counts); ?>
            };

            // 检查数据是否有效
            const chart3Data = {
                labels: (phpData3.labels && phpData3.labels.length > 0) ? phpData3.labels : testData.合同类型,
                counts: (phpData3.counts && phpData3.counts.length > 0) ? phpData3.counts : testData.合同类型数量
            };

            console.log('图表3数据:', chart3Data);

            const chart3 = new Chart(contractTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: chart3Data.labels,
                    datasets: [{
                        data: chart3Data.counts,
                        backgroundColor: [
                            '#1e88e5',
                            '#43a047',
                            '#ff9800',
                            '#e53935',
                            '#9c27b0'
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed + '个 (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart3, '暂无合同类型数据，系统支持多种合同类型');

            // 4. 合同执行状态图表
            const contractStatusCtx = document.getElementById('contractStatusChart').getContext('2d');

            // 获取PHP数据
            const phpData4 = {
                labels: <?php echo json_encode($contract_status); ?>,
                counts: <?php echo json_encode($contract_status_counts); ?>
            };

            // 检查数据是否有效
            const chart4Data = {
                labels: (phpData4.labels && phpData4.labels.length > 0) ? phpData4.labels : ['未开始', '执行中', '已完成'],
                counts: (phpData4.counts && phpData4.counts.length > 0) ? phpData4.counts : [3, 8, 4]
            };

            console.log('图表4数据:', chart4Data);

            const chart4 = new Chart(contractStatusCtx, {
                type: 'pie',
                data: {
                    labels: chart4Data.labels,
                    datasets: [{
                        data: chart4Data.counts,
                        backgroundColor: ['#43a047', '#ff9800', '#f44336'],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart4, '暂无合同执行状态数据，请检查合同进度');

            // 5. 合同金额分布图表
            const contractAmountCtx = document.getElementById('contractAmountChart').getContext('2d');

            // 使用测试数据
            const chart5Data = {
                labels: ['50万以下', '50-100万', '100-200万', '200-500万', '500万以上'],
                counts: [3, 5, 4, 2, 1]
            };

            console.log('图表5数据:', chart5Data);

            const chart5 = new Chart(contractAmountCtx, {
                type: 'bar',
                data: {
                    labels: chart5Data.labels,
                    datasets: [{
                        label: '合同数量',
                        data: chart5Data.counts,
                        backgroundColor: [
                            '#e3f2fd',
                            '#bbdefb',
                            '#90caf9',
                            '#64b5f6',
                            '#42a5f5'
                        ],
                        borderColor: '#1e88e5',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '合同数量'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart5, '暂无合同金额分布数据，显示标准金额档次');

            // 6. 收款进度分析图表
            const paymentProgressCtx = document.getElementById('paymentProgressChart').getContext('2d');

            // 使用测试数据
            const chart6Data = {
                labels: ['项目A', '项目B', '项目C', '项目D', '项目E'],
                progress: [85, 92, 67, 78, 95]
            };

            console.log('图表6数据:', chart6Data);

            const chart6 = new Chart(paymentProgressCtx, {
                type: 'bar',
                data: {
                    labels: chart6Data.labels,
                    datasets: [{
                        label: '收款进度(%)',
                        data: chart6Data.progress,
                        backgroundColor: function(context) {
                            const value = context.parsed.y;
                            if (value >= 80) return '#4caf50';
                            if (value >= 60) return '#ff9800';
                            if (value >= 40) return '#ffeb3b';
                            return '#f44336';
                        },
                        borderColor: '#ffffff',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '收款进度(%)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed.y + '%';
                                }
                            }
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart6, '暂无收款进度数据，请检查合同收款情况');

            // 7. 收款时效分析图表
            const paymentTimingCtx = document.getElementById('paymentTimingChart').getContext('2d');

            // 使用测试数据
            const chart7Data = {
                labels: ['30天内', '31-90天', '91-180天', '超过180天', '未收款'],
                counts: [8, 5, 3, 2, 1]
            };

            console.log('图表7数据:', chart7Data);

            const chart7 = new Chart(paymentTimingCtx, {
                type: 'doughnut',
                data: {
                    labels: chart7Data.labels,
                    datasets: [{
                        data: chart7Data.counts,
                        backgroundColor: [
                            '#4caf50',  // 30天内 - 绿色
                            '#8bc34a',  // 31-90天 - 浅绿
                            '#ffeb3b',  // 91-180天 - 黄色
                            '#ff9800',  // 超过180天 - 橙色
                            '#f44336'   // 未收款 - 红色
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed + '个 (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart7, '暂无收款时效数据，显示标准时效分类');

            // 8. 收款风险评估图表
            const riskAssessmentCtx = document.getElementById('riskAssessmentChart').getContext('2d');

            // 使用测试数据
            const chart8Data = {
                labels: ['无风险', '低风险', '中风险', '高风险', '极高风险'],
                counts: [6, 4, 3, 2, 1]
            };

            console.log('图表8数据:', chart8Data);

            const chart8 = new Chart(riskAssessmentCtx, {
                type: 'pie',
                data: {
                    labels: chart8Data.labels,
                    datasets: [{
                        data: chart8Data.counts,
                        backgroundColor: [
                            '#4caf50',  // 无风险 - 绿色
                            '#8bc34a',  // 低风险 - 浅绿
                            '#ffeb3b',  // 中风险 - 黄色
                            '#ff9800',  // 高风险 - 橙色
                            '#f44336'   // 极高风险 - 红色
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed + '个 (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart8, '暂无收款风险数据，显示风险评估框架');

            // 9. 合同产值对比分析图表
            const contractValueCtx = document.getElementById('contractValueChart').getContext('2d');

            // 使用测试数据
            const chart9Data = {
                labels: ['项目A', '项目B', '项目C', '项目D', '项目E'],
                contractAmounts: [500, 800, 300, 600, 400],
                completedValues: [450, 720, 280, 540, 380]
            };

            console.log('图表9数据:', chart9Data);

            const chart9 = new Chart(contractValueCtx, {
                type: 'bar',
                data: {
                    labels: chart9Data.labels,
                    datasets: [
                        {
                            label: '合同金额(万)',
                            data: chart9Data.contractAmounts,
                            backgroundColor: '#1e88e5',
                            borderColor: '#1565c0',
                            borderWidth: 1
                        },
                        {
                            label: '完成产值(万)',
                            data: chart9Data.completedValues,
                            backgroundColor: '#43a047',
                            borderColor: '#2e7d32',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ¥' + context.parsed.y + '万';
                                }
                            }
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart9, '暂无合同产值对比数据，请检查项目产值记录');

            // 10. 月度合同签订与产值完成趋势图表
            const monthlyTrendCtx = document.getElementById('monthlyTrendChart').getContext('2d');

            // 使用测试数据
            const chart10Data = {
                labels: ['01月', '02月', '03月', '04月', '05月', '06月'],
                contractData: [300, 450, 200, 500, 350, 400],
                valueData: [280, 420, 180, 480, 330, 380]
            };

            console.log('图表10数据:', chart10Data);

            const chart10 = new Chart(monthlyTrendCtx, {
                type: 'line',
                data: {
                    labels: chart10Data.labels,
                    datasets: [
                        {
                            label: '合同签订金额(万)',
                            data: chart10Data.contractData,
                            borderColor: '#1e88e5',
                            backgroundColor: 'rgba(30, 136, 229, 0.1)',
                            yAxisID: 'y',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '产值完成金额(万)',
                            data: chart10Data.valueData,
                            borderColor: '#43a047',
                            backgroundColor: 'rgba(67, 160, 71, 0.1)',
                            yAxisID: 'y',
                            tension: 0.3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ¥' + context.parsed.y + '万';
                                }
                            }
                        }
                    }
                }
            });

            // 添加无数据检查
            addNoDataMessage(chart10, '暂无月度趋势数据，显示时间轴框架');
        }

        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化图表...');

            // 调试：检查所有数据
            console.log('=== 数据调试信息 ===');
            console.log('1. 收款日期:', <?php echo json_encode($收款日期); ?>);
            console.log('2. 预计收款金额:', <?php echo json_encode($预计收款金额); ?>);
            console.log('3. 已收金额:', <?php echo json_encode($已收金额); ?>);
            console.log('4. 合同月份:', <?php echo json_encode($contract_months); ?>);
            console.log('5. 合同数量:', <?php echo json_encode($contract_counts); ?>);
            console.log('6. 合同类型:', <?php echo json_encode($contract_types); ?>);
            console.log('7. 合同类型数量:', <?php echo json_encode($contract_type_counts); ?>);
            console.log('8. 总合同数:', <?php echo $total_contracts; ?>);
            console.log('==================');

            initCharts();

            // 检查是否有数据，如果没有则显示提示
            const totalContracts = <?php echo $total_contracts; ?>;
            if (totalContracts === 0) {
                // 创建更人性化的无数据提示
                const noDataDiv = document.createElement('div');
                noDataDiv.className = 'alert alert-light border';
                noDataDiv.style.cssText = `
                    margin: 20px 0;
                    padding: 30px;
                    text-align: center;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-radius: 15px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                `;

                noDataDiv.innerHTML = `
                    <div style="font-size: 64px; margin-bottom: 20px; color: #6c757d;">📊</div>
                    <h4 style="color: #495057; margin-bottom: 15px;">欢迎使用项目合同汇总分析</h4>
                    <p style="color: #6c757d; font-size: 16px; margin-bottom: 25px;">
                        当前系统中暂无合同数据，图表将展示完整的分析框架供您参考
                    </p>

                    <div style="background: white; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: left;">
                        <h6 style="color: #28a745; margin-bottom: 15px;">
                            <i class="fas fa-lightbulb" style="margin-right: 8px;"></i>快速开始指南
                        </h6>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <strong style="color: #007bff;">📝 数据录入</strong>
                                <ul style="margin: 8px 0; padding-left: 20px; color: #6c757d;">
                                    <li>录入合同基本信息</li>
                                    <li>添加收费计划和记录</li>
                                    <li>更新项目产值数据</li>
                                </ul>
                            </div>
                            <div>
                                <strong style="color: #28a745;">🔍 数据查看</strong>
                                <ul style="margin: 8px 0; padding-left: 20px; color: #6c757d;">
                                    <li>调整时间范围筛选</li>
                                    <li>查看不同维度分析</li>
                                    <li>导出分析报告</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 25px;">
                        <button class="btn btn-primary" onclick="window.location.reload()" style="margin-right: 10px;">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                        <button class="btn btn-outline-secondary" onclick="document.getElementById('start-date').focus()">
                            <i class="fas fa-calendar-alt"></i> 调整时间范围
                        </button>
                    </div>
                `;

                // 插入到第一个卡片前面
                const firstCard = document.querySelector('.card');
                if (firstCard) {
                    firstCard.parentNode.insertBefore(noDataDiv, firstCard);
                }

                // 为所有图表容器添加淡化效果
                const chartContainers = document.querySelectorAll('.chart-container');
                chartContainers.forEach(container => {
                    container.style.opacity = '0.6';
                    container.style.position = 'relative';
                });
            }
        });
    </script>
</body>
</html> 